
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36310.24 d17.14
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = ".Solution Items", ".Solution Items", "{EF739986-3E4D-4628-A0B0-880B6179640D}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		..\.github\workflows\iis-artifact.yml = ..\.github\workflows\iis-artifact.yml
		..\.github\workflows\azure-deploy.yml = ..\.github\workflows\azure-deploy.yml
		openapi-easyshop-datema.json = openapi-easyshop-datema.json
		..\README.md = ..\README.md
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bonus.Shared", "Bonus.Shared\Bonus.Shared.csproj", "{5052FF93-5107-4F99-8234-4DE3B85715DF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bonus.Adapters", "Bonus.Adapters\Bonus.Adapters.csproj", "{57D33343-360F-4EA8-B005-E89755A94FF0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bonus.Core", "Bonus.Core\Bonus.Core.csproj", "{B8E4272A-8A39-4606-B785-7953BDBC8991}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bonus.Services", "Bonus.Services\Bonus.Services.csproj", "{BD035A58-1026-4EF2-BB87-94EE4B196C4E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bonus.Api", "Bonus.Api\Bonus.Api.csproj", "{8D9DB7E0-964C-44B0-A5C8-DDA2E216E038}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{E82B3179-6957-4662-BA42-AEDA6F6A5809}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{95479795-BE65-4CB9-9F6F-17A3A928C3AB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Web", "Web", "{EE7D8CD7-3CD4-450E-8B2C-65402DB3EE5A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bonus.Resources", "Bonus.Resources\Bonus.Resources.csproj", "{944FECC6-FFB4-46CC-B789-AB952673912F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bonus.Core.Settings", "Bonus.Core.Settings\Bonus.Core.Settings.csproj", "{59D44384-2E8E-4313-B6DD-FE5CAAF0FBDC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bonus.TestClient", "Bonus.TestClient\Bonus.TestClient.csproj", "{8A668ED3-D31E-4E8F-9633-04AE24638DB8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Bonus.BackgroundJobs", "Bonus.BackgroundJobs\Bonus.BackgroundJobs.csproj", "{05F97E26-3725-4856-BC1C-712F2A8A326C}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5052FF93-5107-4F99-8234-4DE3B85715DF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5052FF93-5107-4F99-8234-4DE3B85715DF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5052FF93-5107-4F99-8234-4DE3B85715DF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5052FF93-5107-4F99-8234-4DE3B85715DF}.Release|Any CPU.Build.0 = Release|Any CPU
		{57D33343-360F-4EA8-B005-E89755A94FF0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{57D33343-360F-4EA8-B005-E89755A94FF0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{57D33343-360F-4EA8-B005-E89755A94FF0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{57D33343-360F-4EA8-B005-E89755A94FF0}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8E4272A-8A39-4606-B785-7953BDBC8991}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8E4272A-8A39-4606-B785-7953BDBC8991}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8E4272A-8A39-4606-B785-7953BDBC8991}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8E4272A-8A39-4606-B785-7953BDBC8991}.Release|Any CPU.Build.0 = Release|Any CPU
		{BD035A58-1026-4EF2-BB87-94EE4B196C4E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BD035A58-1026-4EF2-BB87-94EE4B196C4E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BD035A58-1026-4EF2-BB87-94EE4B196C4E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BD035A58-1026-4EF2-BB87-94EE4B196C4E}.Release|Any CPU.Build.0 = Release|Any CPU
		{8D9DB7E0-964C-44B0-A5C8-DDA2E216E038}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8D9DB7E0-964C-44B0-A5C8-DDA2E216E038}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8D9DB7E0-964C-44B0-A5C8-DDA2E216E038}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8D9DB7E0-964C-44B0-A5C8-DDA2E216E038}.Release|Any CPU.Build.0 = Release|Any CPU
		{944FECC6-FFB4-46CC-B789-AB952673912F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{944FECC6-FFB4-46CC-B789-AB952673912F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{944FECC6-FFB4-46CC-B789-AB952673912F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{944FECC6-FFB4-46CC-B789-AB952673912F}.Release|Any CPU.Build.0 = Release|Any CPU
		{59D44384-2E8E-4313-B6DD-FE5CAAF0FBDC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{59D44384-2E8E-4313-B6DD-FE5CAAF0FBDC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{59D44384-2E8E-4313-B6DD-FE5CAAF0FBDC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{59D44384-2E8E-4313-B6DD-FE5CAAF0FBDC}.Release|Any CPU.Build.0 = Release|Any CPU
		{8A668ED3-D31E-4E8F-9633-04AE24638DB8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8A668ED3-D31E-4E8F-9633-04AE24638DB8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8A668ED3-D31E-4E8F-9633-04AE24638DB8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8A668ED3-D31E-4E8F-9633-04AE24638DB8}.Release|Any CPU.Build.0 = Release|Any CPU
		{05F97E26-3725-4856-BC1C-712F2A8A326C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{05F97E26-3725-4856-BC1C-712F2A8A326C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{05F97E26-3725-4856-BC1C-712F2A8A326C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{05F97E26-3725-4856-BC1C-712F2A8A326C}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{5052FF93-5107-4F99-8234-4DE3B85715DF} = {E82B3179-6957-4662-BA42-AEDA6F6A5809}
		{57D33343-360F-4EA8-B005-E89755A94FF0} = {95479795-BE65-4CB9-9F6F-17A3A928C3AB}
		{B8E4272A-8A39-4606-B785-7953BDBC8991} = {95479795-BE65-4CB9-9F6F-17A3A928C3AB}
		{BD035A58-1026-4EF2-BB87-94EE4B196C4E} = {95479795-BE65-4CB9-9F6F-17A3A928C3AB}
		{8D9DB7E0-964C-44B0-A5C8-DDA2E216E038} = {EE7D8CD7-3CD4-450E-8B2C-65402DB3EE5A}
		{944FECC6-FFB4-46CC-B789-AB952673912F} = {E82B3179-6957-4662-BA42-AEDA6F6A5809}
		{59D44384-2E8E-4313-B6DD-FE5CAAF0FBDC} = {95479795-BE65-4CB9-9F6F-17A3A928C3AB}
		{05F97E26-3725-4856-BC1C-712F2A8A326C} = {95479795-BE65-4CB9-9F6F-17A3A928C3AB}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {5F2CB75D-BCFA-41E2-AA40-34DAED04188B}
	EndGlobalSection
EndGlobal

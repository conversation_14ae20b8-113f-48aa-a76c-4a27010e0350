using Bonus.BackgroundJobs.Jobs.Queued;
using Bonus.Core.Services;
using Hangfire;

namespace Bonus.BackgroundJobs;

public class BackgroundJobService(IBackgroundJobClient backgroundJobClient) : IBackgroundJobService
{
    public async Task SyncSalesEntries(string cardId)
    {
        backgroundJobClient.Enqueue<ILSRetailJobs>(x => x.SyncSalesEntries(cardId));
    }

    public async Task SyncSalesEntry(string salesEntryId)
    {
        backgroundJobClient.Enqueue<ILSRetailJobs>(x => x.SyncSalesEntry(salesEntryId));
    }
}

using Bonus.BackgroundJobs.Jobs.Recurring.Base;
using Hangfire;

namespace Bonus.BackgroundJobs;

public class RecurringJobService(IRecurringJobManager recurringJobManager) : IRecurringJobService
{
    public void AddOrUpdate<T>(string jobName, string cron)
        where T : IRecurringJob
    {
        recurringJobManager.AddOrUpdate<T>(jobName, x => x.RunRecurring(jobName), cron);
    }
}

public interface IRecurringJobService
{
    void AddOrUpdate<T>(string jobName, string cron)
        where T : IRecurringJob;
}
using Bonus.Core.Handlers.SalesEntries;
using MediatR;

namespace Bonus.BackgroundJobs.Jobs.Queued;

public interface ILSRetailJobs
{
    Task SyncSalesEntries(string cardId);

    Task SyncSalesEntry(string salesEntryId);
}

public class LSRetailJobs(IMediator mediator) : ILSRetailJobs
{
    public async Task SyncSalesEntries(string cardId)
    {
        var request = new SyncSalesEntriesForUserRequest
        {
            CardId = cardId
        };

        await mediator.Send(request);
    }

    public async Task SyncSalesEntry(string salesEntryId)
    {
        var request = new SyncSalesEntryRequest
        {
            EntryId = salesEntryId
        };

        await mediator.Send(request);
    }
}
using Bonus.BackgroundJobs.Jobs.Recurring.Base;
using Bonus.Core.Handlers.SalesEntries;
using MediatR;

namespace Bonus.BackgroundJobs.Jobs.Recurring;

public class SyncSalesEntriesJob(IMediator mediator) : BaseRec<PERSON>ring<PERSON><PERSON>(mediator), ISyncSalesEntriesJob
{
    protected override async Task Execute()
    {
        var request = new SyncSalesEntriesRequest();
        await mediator.Send(request);
    }
}
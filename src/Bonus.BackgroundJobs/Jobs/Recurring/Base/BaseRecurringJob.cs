using Bonus.Core.Handlers.Jobs;
using MediatR;

namespace Bonus.BackgroundJobs.Jobs.Recurring.Base;

public abstract class BaseRecurringJob(IMediator mediator) : IRecurringJob
{
    protected abstract Task Execute();

    public async Task RunRecurring()
    {
        // Use the RunRecurring(string) method that's called from Hangfire
        // This way we don't need to store the name in the job class
        throw new NotImplementedException("This method should not be called directly. Use RunRecurring(string) instead.");
    }

    private async Task<bool> ShouldJobRun(string name)
    {
        var request = new CanRecurringJobRunRequest
        {
            Name = name
        };

        var response = await mediator.Send(request);

        return response.ShouldJobRun;
    }

    public async Task RunRecurring(string jobName)
    {
        var result = await ShouldJobRun(jobName);

        if (result)
        {
            await Execute();
        }
    }
}
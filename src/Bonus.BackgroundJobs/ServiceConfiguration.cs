using Bonus.BackgroundJobs.Constants;
using Bonus.BackgroundJobs.Jobs.Queued;
using Bonus.BackgroundJobs.Jobs.Recurring;
using Bonus.BackgroundJobs.Jobs.Recurring.Base;
using Bonus.Core.Services;
using Hangfire;
using Hangfire.SqlServer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Bonus.BackgroundJobs;

public static class ServiceConfiguration
{
    public static void AddBackgroundJobs(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddHangfire(hangfireConfiguration => hangfireConfiguration
            .SetDataCompatibilityLevel(CompatibilityLevel.Version_170)
            .UseSimpleAssemblyNameTypeSerializer()
            .UseRecommendedSerializerSettings()
            .UseFilter(
                new AutomaticRetryAttribute
                {
                    Attempts = 0
                })
            .UseSqlServerStorage(
                configuration.GetConnectionString("Hangfire"),
                new SqlServerStorageOptions
                {
                    SchemaName = "hangfire",
                    PrepareSchemaIfNecessary = true,
                    QueuePollInterval = TimeSpan.FromMilliseconds(200),
                })
        );

        services.AddTransient<IBackgroundJobService, BackgroundJobService>();
    }

    public static void AddBonusHangfireServer(this IServiceCollection services)
    {
        services.AddHangfireServer(options =>
        {
            options.Queues = [JobPriorityQueues.High, JobPriorityQueues.Default, JobPriorityQueues.Low];
            options.SchedulePollingInterval = TimeSpan.FromMilliseconds(500);
        });

        // Jobs
        services.AddTransient<ILSRetailJobs, LSRetailJobs>();

        // Recurring jobs
        services.AddTransient<ISyncSalesEntriesJob, SyncSalesEntriesJob>();
        services.AddTransient<ISyncCatalogueJob, SyncCatalogueJob>();

        // Recurring job service
        services.AddTransient<IRecurringJobService, RecurringJobService>();

        services.AddHostedService<RecurringJobScheduler>();
    }
}

using Bonus.BackgroundJobs.Jobs.Recurring.Base;
using Hangfire;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Bonus.BackgroundJobs;

public class RecurringJobScheduler(IRecurringJobService recurringJobService, ILogger<RecurringJobScheduler> logger) : BackgroundService
{
    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        logger.LogInformation("Started scheduling jobs");

        recurringJobService.AddOrUpdate<ISyncSalesEntriesJob>("sync-sales-entries", Cron.Daily(0));
        recurringJobService.AddOrUpdate<ISyncCatalogueJob>("sync-catalogue", Cron.Daily(7));

        logger.LogInformation("Finished scheduling jobs");

        return Task.CompletedTask;
    }
}

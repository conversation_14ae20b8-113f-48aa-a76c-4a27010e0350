using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Models.Requests;
using Bonus.Services.Services;
using Bonus.Shared.Types.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Bonus.Api.Attributes;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class ValidEmailAttribute : Attribute, IAsyncAuthorizationFilter
{
    private readonly bool _ignore;
    
    public ValidEmailAttribute(bool ignore = false)
    {
        _ignore = ignore;
    }

    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        if (_ignore)
        {
            return;
        }

        var user = context.HttpContext.RequestServices.GetRequiredService<IBonusUser>();
        var lsRetailAdapter = context.HttpContext.RequestServices.GetRequiredService<ILSRetailAdapter>();
        var accountService = context.HttpContext.RequestServices.GetRequiredService<IAccountService>();

        if (string.IsNullOrEmpty(user.CardId))
        {
            context.Result = new ForbidResult();
            return;
        }

        var contactRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = user.CardId,
            NumberOfTransactionsReturned = 0,
        };

        var contactResponse = await lsRetailAdapter.GetContactByCardId(contactRequest, CancellationToken.None);
        
        var hasValidEmail = contactResponse.Success && 
                           contactResponse.Response?.Contact?.Email is not null && 
                           !accountService.IsTempEmail(contactResponse.Response.Contact.Email);

        if (!hasValidEmail)
        {
            context.Result = new ForbidResult();
        }
    }
}

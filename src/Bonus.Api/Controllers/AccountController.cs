using Bonus.Api.Constants;
using Bonus.Services.Handlers.Account;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers;

[Authorize]
[ApiController]
[Route("api/account")]
[ApiExplorerSettings(GroupName = ApiVersions.Current)]
public class AccountController(IMediator mediator) : ControllerBase
{
    [HttpGet]
    public async Task<GetAccountResponse> GetAccount(CancellationToken cancellationToken)
    {
        return await mediator.Send(new GetAccountRequest(), cancellationToken);
    }

    [HttpPost("accept-terms")]
    public async Task<AcceptTermsResponse> AcceptTerms([FromBody] AcceptTermsRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpPut("name")]
    public async Task<UpdateNameResponse> UpdateName([FromBody] UpdateNameRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpPut("email")]
    public async Task<UpdateEmailResponse> UpdateEmail([FromBody] UpdateEmailRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpPut("external-user-notification-id")]
    public async Task UpdateExternalUserNotificationId([FromBody] UpdateExternalUserNotificationIdRequest request, CancellationToken cancellationToken)
    {
        await mediator.Send(request, cancellationToken);
    }

    [HttpDelete("external-user-notification-id")]
    public async Task DeleteExternalUserNotificationId(CancellationToken cancellationToken)
    {
        var request = new DeleteExternalUserNotificationIdRequest();
        await mediator.Send(request, cancellationToken);
    }

    [HttpDelete]
    public async Task<DeleteAccountResponse> DeleteAccount([FromBody] DeleteAccountRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [AllowAnonymous]
    [HttpGet("get-terms-url")]
    public async Task<GetTermsUrlResponse> GetTermsUrl(CancellationToken cancellationToken)
    {
        return await mediator.Send(new GetTermsUrlRequest(), cancellationToken);
    }
}

using Bonus.Api.Constants;
using Bonus.Api.Helpers;
using Bonus.Services.Handlers.Auth;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers;

[ApiController]
[Route("api/auth")]
[ApiExplorerSettings(GroupName = ApiVersions.Current)]
public class AuthController(IMediator mediator) : ControllerBase
{

    [AllowAnonymous]
    [HttpPost("refresh")]
    public async Task<RefreshTokenResponse> RefreshToken([FromBody] RefreshTokenWebRequest webRequest, CancellationToken cancellationToken)
    {
        var request = new RefreshTokenRequest
        {
            RefreshToken = webRequest.RefreshToken,
            AppVersion = webRequest.DeviceInfo.AppVersion,
            DeviceId = webRequest.DeviceInfo.DeviceId,
            DeviceManufacturer = webRequest.DeviceInfo.DeviceManufacturer,
            DeviceModelName = webRequest.DeviceInfo.DeviceModelName,
            OperatingSystem = webRequest.DeviceInfo.OperatingSystem,
            OperatingSystemVersion = webRequest.DeviceInfo.OperatingSystemVersion,
            IpAddress = HttpContext.GetClientIpAddress()
        };

        return await mediator.Send(request, cancellationToken);
    }

    [AllowAnonymous]
    [HttpPost("start")]
    public async Task<StartHagarIdAuthResponse> StartHagarIdAuth([FromBody] StartHagarIdAuthWebRequest webRequest, CancellationToken cancellationToken)
    {
        var request = new StartHagarIdAuthRequest
        {
            PhoneNumber = webRequest.PhoneNumber,
            NationalId = webRequest.NationalId,
            DeviceId = webRequest.DeviceInfo.DeviceId,
            DeviceManufacturer = webRequest.DeviceInfo.DeviceManufacturer,
            DeviceModelName = webRequest.DeviceInfo.DeviceModelName,
            OperatingSystem = webRequest.DeviceInfo.OperatingSystem,
            OperatingSystemVersion = webRequest.DeviceInfo.OperatingSystemVersion,
            AppVersion = webRequest.DeviceInfo.AppVersion,
            IpAddress = HttpContext.GetClientIpAddress()
        };

        return await mediator.Send(request, cancellationToken);
    }

    [AllowAnonymous]
    [HttpPost("check")]
    public async Task<CheckHagarIdAuthResponse> CheckHagarIdAuth([FromBody] CheckHagarIdAuthWebRequest webRequest, CancellationToken cancellationToken)
    {
        var request = new CheckHagarIdAuthRequest
        {
            SessionId = webRequest.SessionId,
            DeviceId = webRequest.DeviceId,
            DeviceModelName = webRequest.DeviceModelName,
        };

        return await mediator.Send(request, cancellationToken);
    }
}

public class LoginWebRequest
{
    public string? PhoneNumber { get; set; }

    public string? Ssn { get; set; }

    public required DeviceInfoData DeviceInfo { get; set; }
}

public class RefreshTokenWebRequest
{
    public string? RefreshToken { get; set; }

    public required DeviceInfoData DeviceInfo { get; set; }
}

public class RegisterWebRequest
{
    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public string? Email { get; set; }

    public string? AuthenticationId { get; set; }

    public string? PhoneNumber { get; set; }

    public string? Ssn { get; set; }

    public required DeviceInfoData DeviceInfo { get; set; }
}

public class DeviceInfoData
{
    public string? DeviceId { get; set; }

    public string? DeviceManufacturer { get; set; }

    public string? DeviceModelName { get; set; }

    public string? OperatingSystem { get; set; }

    public string? OperatingSystemVersion { get; set; }

    public string? AppVersion { get; set; }
}

public class StartHagarIdAuthWebRequest
{
    public string? PhoneNumber { get; set; }
    public string? NationalId { get; set; }
    public required DeviceInfoData DeviceInfo { get; set; }
}

public class CheckHagarIdAuthWebRequest
{
    public required string SessionId { get; set; }

    public required string DeviceId { get; set; }

    public required string DeviceModelName { get; set; }
}

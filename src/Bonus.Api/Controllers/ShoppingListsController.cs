using Bonus.Api.Constants;
using Bonus.Services.Handlers.ShoppingList;
using Bonus.Services.Services;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers;

[Authorize]
[ApiController]
[Route("api/shopping-lists")]
[ApiExplorerSettings(GroupName = ApiVersions.Current)]
public class ShoppingListsController(IMediator mediator)
{
    [HttpGet]
    public async Task<GetShoppingListsResponse> GetShoppingLists(CancellationToken cancellationToken)
    {
        return await mediator.Send(new GetShoppingListsRequest(), cancellationToken);
    }

    [HttpGet("details")]
    public async Task<ResultShoppingList> GetShoppingListDetails([FromQuery] string id, CancellationToken cancellationToken)
    {
        var request = new GetShoppingListRequest
        {
            Id = id
        };

        return await mediator.Send(request, cancellationToken);
    }

    [HttpPost]
    public async Task<ResultShoppingList> CreateShoppingList([FromBody] CreateShoppingListRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpPut]
    public async Task<ResultShoppingList> UpdateShoppingList([FromBody] UpdateShoppingListRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpDelete]
    public async Task<RemoveShoppingListResponse> RemoveShoppingList([FromBody] RemoveShoppingListRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpPost("item")]
    public async Task<ResultShoppingList> AddShoppingListItem([FromBody] AddShoppingListItemRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpPost("items")]
    public async Task<ResultShoppingList> AddShoppingListItems([FromBody] AddShoppingListItemsRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpPut("item")]
    public async Task<ResultShoppingList> UpdateShoppingListItem([FromBody] UpdateShoppingListItemRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpPost("item/complete")]
    public async Task<ResultShoppingList> CompleteShoppingListItems([FromBody] CompleteShoppingListItemsRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpDelete("item")]
    public async Task<ResultShoppingList> RemoveShoppingListItems([FromBody] RemoveShoppingListItemsRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpPost("share")]
    public async Task<ResultShoppingList> ShareShoppingList([FromBody] ShareShoppingListRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpDelete("share")]
    public async Task<ResultShoppingList> RemoveShoppingListShare([FromBody] RemoveShoppingListShareRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }

    [HttpPost("share/resolve")]
    public async Task<ResolveShoppingListShareResponse> ResolveShoppingListShare([FromBody] ResolveShoppingListShareRequest request, CancellationToken cancellationToken)
    {
        return await mediator.Send(request, cancellationToken);
    }
}

using Bonus.Api.Constants;
using Bonus.Services.Handlers.Catalogue;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bonus.Api.Controllers;

[ApiController]
[Route("api/catalogue")]
[ApiExplorerSettings(GroupName = ApiVersions.Current)]
public class CatalogueController(IMediator mediator)
{
    [AllowAnonymous]
    [HttpGet("categories")]
    public async Task<GetCategoriesResponse> GetCategories(CancellationToken cancellationToken)
    {
        return await mediator.Send(new GetCategoriesRequest(), cancellationToken);
    }

    [AllowAnonymous]
    [HttpGet("category")]
    public async Task<GetCategoryResponse> GetCategory([FromQuery] string id, CancellationToken cancellationToken)
    {
        var request = new GetCategoryRequest
        {
            Id = id
        };

        return await mediator.Send(request, cancellationToken);
    }

    [AllowAnonymous]
    [HttpGet("item")]
    public async Task<GetItemDetailsResponse> GetItemDetails([FromQuery] string id, CancellationToken cancellationToken)
    {
        var request = new GetItemDetailsRequest
        {
            Id = id
        };

        return await mediator.Send(request, cancellationToken);
    }

    [AllowAnonymous]
    [HttpGet("search")]
    public async Task<SearchCatalogueResponse> SearchCatalogue([FromQuery] string query, [FromQuery] int limit, CancellationToken cancellationToken)
    {
        var request = new SearchCatalogueRequest
        {
            Query = query,
            Limit = limit
        };

        return await mediator.Send(request, cancellationToken);
    }

    [AllowAnonymous]
    [HttpGet("full")]
    public async Task<GetFullCatalogueResponse> GetFullCatalogue([FromQuery] DateTime? lastUpdated, CancellationToken cancellationToken)
    {
        var request = new GetFullCatalogueRequest
        {
            LastUpdated = lastUpdated
        };

        return await mediator.Send(request, cancellationToken);
    }
}

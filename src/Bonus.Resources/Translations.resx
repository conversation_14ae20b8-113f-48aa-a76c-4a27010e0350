<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
        <xsd:element name="root" msdata:IsDataSet="true">

        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
    </resheader>
    <data name="Auth_PhoneOrSsnRequired" xml:space="preserve">
      <value>Phone number or SSN must be provided.</value>
    </data>
    <data name="Auth_DeviceIdRequired" xml:space="preserve">
      <value>Device ID must be provided.</value>
    </data>
    <data name="Login_UserNotFound" xml:space="preserve">
      <value>User not found.</value>
    </data>
    <data name="Login_AudkenniFailed" xml:space="preserve">
      <value>Audkenni login failed.</value>
    </data>
    <data name="Login_AudkenniSettingsNotFound" xml:space="preserve">
      <value>Required Audkenni settings not found.</value>
    </data>
    <data name="Login_HagarIdFailed" xml:space="preserve">
      <value>HagarId authentication failed.</value>
    </data>
  <data name="Register_EmailRequired" xml:space="preserve">
      <value>Email must be provided.</value>
    </data>
    <data name="Register_AuthIdRequired" xml:space="preserve">
      <value>Authentication ID must be provided.</value>
    </data>
    <data name="Register_ContactCreateFailed" xml:space="preserve">
      <value>Failed to create contact.</value>
    </data>
    <data name="Token_RefreshTokenRequired" xml:space="preserve">
      <value>Refresh token is required.</value>
    </data>
    <data name="Token_RefreshTokenNotFound" xml:space="preserve">
      <value>Refresh token not found.</value>
    </data>
    <data name="Token_RefreshTokenExpired" xml:space="preserve">
      <value>Refresh token expired.</value>
    </data>
    <data name="Token_RefreshTokenUsed" xml:space="preserve">
      <value>Refresh token already used.</value>
    </data>
  <data name="Account_ContactNotFound" xml:space="preserve">
    <value>Account not found.</value>
  </data>
    <data name="Account_UpdateEmailFailed" xml:space="preserve">
      <value>Failed to update email.</value>
    </data>
    <data name="Account_AcceptTermsFailed" xml:space="preserve">
      <value>Failed to accept terms.</value>
    </data>
    <data name="Account_NoAddressesFound" xml:space="preserve">
      <value>No addresses found for the contact.</value>
    </data>
    <data name="Account_UpdatePhoneFailed" xml:space="preserve">
      <value>Failed to update phone number.</value>
    </data>
    <data name="Account_PhoneNotUpdated" xml:space="preserve">
      <value>Phone number was not updated properly.</value>
    </data>
    <data name="ShoppingList_IdRequired" xml:space="preserve">
      <value>Shopping list ID is required.</value>
    </data>
    <data name="ShoppingList_ShareIdRequired" xml:space="preserve">
      <value>Shopping list Share Id is required.</value>
    </data>
    <data name="ShoppingList_NotFound" xml:space="preserve">
      <value>Shopping list not found.</value>
    </data>
    <data name="ShoppingList_ShareNotFound" xml:space="preserve">
      <value>Shopping list share not found.</value>
    </data>
    <data name="ShoppingList_CannotRemoveOwnShare" xml:space="preserve">
      <value>Cannot remove your own share.</value>
    </data>
    <data name="ShoppingList_ShareNotAccepted" xml:space="preserve">
      <value>You are not authorized to make changes to the shopping list.</value>
    </data>
    <data name="ShoppingList_RemoveShareFailed" xml:space="preserve">
      <value>Failed to remove the share.</value>
    </data>
    <data name="ShoppingList_ResolveShareFailed" xml:space="preserve">
      <value>Failed to resolve the share request.</value>
    </data>
    <data name="ShoppingList_InvalidContactMethod" xml:space="preserve">
      <value>Invalid contact method.</value>
    </data>
    <data name="ShoppingList_ContactValueRequired" xml:space="preserve">
      <value>Contact value is required.</value>
    </data>
    <data name="ShoppingList_ShareFailed" xml:space="preserve">
      <value>Failed to share shopping list.</value>
    </data>
  <data name="ShoppingList_ItemIdRequired" xml:space="preserve">
    <value>Item ID is required.</value>
  </data>
  <data name="ShoppingList_ItemNotFound" xml:space="preserve">
    <value>Item not found in shopping list.</value>
  </data>
  <data name="ShoppingList_UpdatedItemNotFound" xml:space="preserve">
    <value>Failed to find updated item in the response.</value>
  </data>
  <data name="ShoppingList_ItemUpdateFailed" xml:space="preserve">
    <value>Failed to update item in shopping list.</value>
  </data>
  <data name="ShoppingList_ItemAddFailed" xml:space="preserve">
    <value>Failed to add item to shopping list.</value>
  </data>
  <data name="UserNotAuthenticated" xml:space="preserve">
    <value>User not authenticated.</value>
  </data>
  <data name="Account_DeleteFailed" xml:space="preserve">
    <value>Failed to delete account.</value>
  </data>
  <data name="Account_UpdatedNameFailed" xml:space="preserve">
    <value>Failed to update name.</value>
  </data>
  <data name="ShoppingList_ItemRemoveFailed" xml:space="preserve">
    <value>Failed to remove item from shopping list.</value>
  </data>
  <data name="ShoppingList_ItemNameRequired" xml:space="preserve">
    <value>Item name is required.</value>
  </data>
  <data name="ShoppingList_NameRequired" xml:space="preserve">
    <value>Name is required.</value>
  </data>
  <data name="ShoppingList_CreatedFailed" xml:space="preserve">
    <value>Failed to create shopping list.</value>
  </data>
  <data name="ShoppingList_UpdateFailed" xml:space="preserve">
    <value>Failed to update shopping list.</value>
  </data>
  <data name="Cart_AddItemFailed" xml:space="preserve">
    <value>Failed to add item to cart.</value>
  </data>
  <data name="Login_HagarIdStartAuthMessage" xml:space="preserve">
    <value>Type in number</value>
  </data>
  <data name="Day_Monday" xml:space="preserve">
    <value>Monday</value>
  </data>
  <data name="Day_Sunday" xml:space="preserve">
    <value>Sunday</value>
  </data>
  <data name="Day_Tuesday" xml:space="preserve">
    <value>Tuesday</value>
  </data>
  <data name="Day_Wednesday" xml:space="preserve">
    <value>Wednesday</value>
  </data>
  <data name="Day_Thursday" xml:space="preserve">
    <value>Thursday</value>
  </data>
  <data name="Day_Friday" xml:space="preserve">
    <value>Friday</value>
  </data>
  <data name="Day_Saturday" xml:space="preserve">
    <value>Saturday</value>
  </data>
  <data name="Catalogue_CategoryNotFound" xml:space="preserve">
    <value>Category {0} not found</value>
  </data>
  <data name="Communication_UnexpectedError" xml:space="preserve">
    <value>Unexpected error occurred while processing the request</value>
  </data>
  <data name="Cart_AddItemFailed_EasyShop" xml:space="preserve">
    <value>Failed to add item. {0}</value>
  </data>
  <data name="Cart_FailedToGetCart_EasyShop" xml:space="preserve">
    <value>Failed to get cart. {0}</value>
  </data>
  <data name="Cart_Checkout_Mismatch" xml:space="preserve">
    <value>Provided barcode {0} does not match the end shopping trip barcode {1}.</value>
  </data>
  <data name="Cart_CheckoutFailed_EasyShop" xml:space="preserve">
    <value>Failed to checkout cart. {0}</value>
  </data>
  <data name="Cart_FailedToClearCart_EasyShop" xml:space="preserve">
    <value>Failed to clear cart. {0}</value>
  </data>
  <data name="Cart_FailedToCreateCart_EasyShop" xml:space="preserve">
    <value>Failed to create cart. {0}</value>
  </data>
  <data name="Cart_FailedToGetCartStatus_EasyShop" xml:space="preserve">
    <value>Failed to get cart status. {0}</value>
  </data>
  <data name="Cart_RemoveItemFailed_EasyShop" xml:space="preserve">
    <value>Failed to remove cart item. {0}</value>
  </data>
  <data name="Cart_UpdateItemFailed_EasyShop" xml:space="preserve">
    <value>Failed to update cart item. {0}</value>
  </data>
  <data name="Catalogue_ItemNotFound" xml:space="preserve">
    <value>Item with ID {0} not found</value>
  </data>
  <data name="Notification_NotFound" xml:space="preserve">
    <value>Notification not found</value>
  </data>
  <data name="Notification_TitleRequired" xml:space="preserve">
    <value>Notification title is required</value>
  </data>
  <data name="Notification_BodyRequired" xml:space="preserve">
    <value>Notification body is required</value>
  </data>
  <data name="Notification_RecipientsCannotBeEmpty" xml:space="preserve">
    <value>Notification must have recipients</value>
  </data>
  <data name="Notification_FailedToSend" xml:space="preserve">
    <value>Failed to send push notification. {0}</value>
  </data>
  <data name="Auth_SessionIdRequired" xml:space="preserve">
    <value>Session ID must be provided</value>
  </data>
  <data name="Auth_SessionNotFound" xml:space="preserve">
    <value>Session is either missing or expired</value>
  </data>
  <data name="Notification_PushTokenMissing" xml:space="preserve">
    <value>User did not opt into notifications</value>
  </data>
    <data name="Email_VerificationSessionNotFound" xml:space="preserve">
        <value>Verification code does not exist</value>
    </data>
    <data name="Email_VerificationSessionExpired" xml:space="preserve">
        <value>Verification code does not exist</value>
    </data>
    <data name="Email_InvalidVerificationCode" xml:space="preserve">
        <value>Verification code is not correct</value>
    </data>
    <data name="Email_VerificationSendFailed" xml:space="preserve">
        <value>Failed to send verification email</value>
    </data>
</root>

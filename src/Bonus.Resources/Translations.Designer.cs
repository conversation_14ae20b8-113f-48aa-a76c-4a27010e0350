//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Bonus.Resources {
    using System;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Translations {
        
        private static System.Resources.ResourceManager resourceMan;
        
        private static System.Globalization.CultureInfo resourceCulture;
        
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Translations() {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        public static System.Resources.ResourceManager ResourceManager {
            get {
                if (object.Equals(null, resourceMan)) {
                    System.Resources.ResourceManager temp = new System.Resources.ResourceManager("Bonus.Resources.Translations", typeof(Translations).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        public static System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        public static string Auth_PhoneOrSsnRequired {
            get {
                return ResourceManager.GetString("Auth_PhoneOrSsnRequired", resourceCulture);
            }
        }
        
        public static string Auth_DeviceIdRequired {
            get {
                return ResourceManager.GetString("Auth_DeviceIdRequired", resourceCulture);
            }
        }
        
        public static string Login_UserNotFound {
            get {
                return ResourceManager.GetString("Login_UserNotFound", resourceCulture);
            }
        }
        
        public static string Login_AudkenniFailed {
            get {
                return ResourceManager.GetString("Login_AudkenniFailed", resourceCulture);
            }
        }
        
        public static string Login_AudkenniSettingsNotFound {
            get {
                return ResourceManager.GetString("Login_AudkenniSettingsNotFound", resourceCulture);
            }
        }
        
        public static string Login_HagarIdFailed {
            get {
                return ResourceManager.GetString("Login_HagarIdFailed", resourceCulture);
            }
        }
        
        public static string Register_EmailRequired {
            get {
                return ResourceManager.GetString("Register_EmailRequired", resourceCulture);
            }
        }
        
        public static string Register_AuthIdRequired {
            get {
                return ResourceManager.GetString("Register_AuthIdRequired", resourceCulture);
            }
        }
        
        public static string Register_ContactCreateFailed {
            get {
                return ResourceManager.GetString("Register_ContactCreateFailed", resourceCulture);
            }
        }
        
        public static string Token_RefreshTokenRequired {
            get {
                return ResourceManager.GetString("Token_RefreshTokenRequired", resourceCulture);
            }
        }
        
        public static string Token_RefreshTokenNotFound {
            get {
                return ResourceManager.GetString("Token_RefreshTokenNotFound", resourceCulture);
            }
        }
        
        public static string Token_RefreshTokenExpired {
            get {
                return ResourceManager.GetString("Token_RefreshTokenExpired", resourceCulture);
            }
        }
        
        public static string Token_RefreshTokenUsed {
            get {
                return ResourceManager.GetString("Token_RefreshTokenUsed", resourceCulture);
            }
        }
        
        public static string Account_ContactNotFound {
            get {
                return ResourceManager.GetString("Account_ContactNotFound", resourceCulture);
            }
        }
        
        public static string Account_UpdateEmailFailed {
            get {
                return ResourceManager.GetString("Account_UpdateEmailFailed", resourceCulture);
            }
        }
        
        public static string Account_AcceptTermsFailed {
            get {
                return ResourceManager.GetString("Account_AcceptTermsFailed", resourceCulture);
            }
        }
        
        public static string Account_NoAddressesFound {
            get {
                return ResourceManager.GetString("Account_NoAddressesFound", resourceCulture);
            }
        }
        
        public static string Account_UpdatePhoneFailed {
            get {
                return ResourceManager.GetString("Account_UpdatePhoneFailed", resourceCulture);
            }
        }
        
        public static string Account_PhoneNotUpdated {
            get {
                return ResourceManager.GetString("Account_PhoneNotUpdated", resourceCulture);
            }
        }
        
        public static string ShoppingList_IdRequired {
            get {
                return ResourceManager.GetString("ShoppingList_IdRequired", resourceCulture);
            }
        }
        
        public static string ShoppingList_ShareIdRequired {
            get {
                return ResourceManager.GetString("ShoppingList_ShareIdRequired", resourceCulture);
            }
        }
        
        public static string ShoppingList_NotFound {
            get {
                return ResourceManager.GetString("ShoppingList_NotFound", resourceCulture);
            }
        }
        
        public static string ShoppingList_ShareNotFound {
            get {
                return ResourceManager.GetString("ShoppingList_ShareNotFound", resourceCulture);
            }
        }
        
        public static string ShoppingList_CannotRemoveOwnShare {
            get {
                return ResourceManager.GetString("ShoppingList_CannotRemoveOwnShare", resourceCulture);
            }
        }
        
        public static string ShoppingList_ShareNotAccepted {
            get {
                return ResourceManager.GetString("ShoppingList_ShareNotAccepted", resourceCulture);
            }
        }
        
        public static string ShoppingList_RemoveShareFailed {
            get {
                return ResourceManager.GetString("ShoppingList_RemoveShareFailed", resourceCulture);
            }
        }
        
        public static string ShoppingList_ResolveShareFailed {
            get {
                return ResourceManager.GetString("ShoppingList_ResolveShareFailed", resourceCulture);
            }
        }
        
        public static string ShoppingList_InvalidContactMethod {
            get {
                return ResourceManager.GetString("ShoppingList_InvalidContactMethod", resourceCulture);
            }
        }
        
        public static string ShoppingList_ContactValueRequired {
            get {
                return ResourceManager.GetString("ShoppingList_ContactValueRequired", resourceCulture);
            }
        }
        
        public static string ShoppingList_ShareFailed {
            get {
                return ResourceManager.GetString("ShoppingList_ShareFailed", resourceCulture);
            }
        }
        
        public static string ShoppingList_ItemIdRequired {
            get {
                return ResourceManager.GetString("ShoppingList_ItemIdRequired", resourceCulture);
            }
        }
        
        public static string ShoppingList_ItemNotFound {
            get {
                return ResourceManager.GetString("ShoppingList_ItemNotFound", resourceCulture);
            }
        }
        
        public static string ShoppingList_UpdatedItemNotFound {
            get {
                return ResourceManager.GetString("ShoppingList_UpdatedItemNotFound", resourceCulture);
            }
        }
        
        public static string ShoppingList_ItemUpdateFailed {
            get {
                return ResourceManager.GetString("ShoppingList_ItemUpdateFailed", resourceCulture);
            }
        }
        
        public static string ShoppingList_ItemAddFailed {
            get {
                return ResourceManager.GetString("ShoppingList_ItemAddFailed", resourceCulture);
            }
        }
        
        public static string UserNotAuthenticated {
            get {
                return ResourceManager.GetString("UserNotAuthenticated", resourceCulture);
            }
        }
        
        public static string Account_DeleteFailed {
            get {
                return ResourceManager.GetString("Account_DeleteFailed", resourceCulture);
            }
        }
        
        public static string Account_UpdatedNameFailed {
            get {
                return ResourceManager.GetString("Account_UpdatedNameFailed", resourceCulture);
            }
        }
        
        public static string ShoppingList_ItemRemoveFailed {
            get {
                return ResourceManager.GetString("ShoppingList_ItemRemoveFailed", resourceCulture);
            }
        }
        
        public static string ShoppingList_ItemNameRequired {
            get {
                return ResourceManager.GetString("ShoppingList_ItemNameRequired", resourceCulture);
            }
        }
        
        public static string ShoppingList_NameRequired {
            get {
                return ResourceManager.GetString("ShoppingList_NameRequired", resourceCulture);
            }
        }
        
        public static string ShoppingList_CreatedFailed {
            get {
                return ResourceManager.GetString("ShoppingList_CreatedFailed", resourceCulture);
            }
        }
        
        public static string ShoppingList_UpdateFailed {
            get {
                return ResourceManager.GetString("ShoppingList_UpdateFailed", resourceCulture);
            }
        }
        
        public static string Cart_AddItemFailed {
            get {
                return ResourceManager.GetString("Cart_AddItemFailed", resourceCulture);
            }
        }
        
        public static string Login_HagarIdStartAuthMessage {
            get {
                return ResourceManager.GetString("Login_HagarIdStartAuthMessage", resourceCulture);
            }
        }
        
        public static string Day_Monday {
            get {
                return ResourceManager.GetString("Day_Monday", resourceCulture);
            }
        }
        
        public static string Day_Sunday {
            get {
                return ResourceManager.GetString("Day_Sunday", resourceCulture);
            }
        }
        
        public static string Day_Tuesday {
            get {
                return ResourceManager.GetString("Day_Tuesday", resourceCulture);
            }
        }
        
        public static string Day_Wednesday {
            get {
                return ResourceManager.GetString("Day_Wednesday", resourceCulture);
            }
        }
        
        public static string Day_Thursday {
            get {
                return ResourceManager.GetString("Day_Thursday", resourceCulture);
            }
        }
        
        public static string Day_Friday {
            get {
                return ResourceManager.GetString("Day_Friday", resourceCulture);
            }
        }
        
        public static string Day_Saturday {
            get {
                return ResourceManager.GetString("Day_Saturday", resourceCulture);
            }
        }
        
        public static string Catalogue_CategoryNotFound {
            get {
                return ResourceManager.GetString("Catalogue_CategoryNotFound", resourceCulture);
            }
        }
        
        public static string Communication_UnexpectedError {
            get {
                return ResourceManager.GetString("Communication_UnexpectedError", resourceCulture);
            }
        }
        
        public static string Cart_AddItemFailed_EasyShop {
            get {
                return ResourceManager.GetString("Cart_AddItemFailed_EasyShop", resourceCulture);
            }
        }
        
        public static string Cart_FailedToGetCart_EasyShop {
            get {
                return ResourceManager.GetString("Cart_FailedToGetCart_EasyShop", resourceCulture);
            }
        }
        
        public static string Cart_Checkout_Mismatch {
            get {
                return ResourceManager.GetString("Cart_Checkout_Mismatch", resourceCulture);
            }
        }
        
        public static string Cart_CheckoutFailed_EasyShop {
            get {
                return ResourceManager.GetString("Cart_CheckoutFailed_EasyShop", resourceCulture);
            }
        }
        
        public static string Cart_FailedToClearCart_EasyShop {
            get {
                return ResourceManager.GetString("Cart_FailedToClearCart_EasyShop", resourceCulture);
            }
        }
        
        public static string Cart_FailedToCreateCart_EasyShop {
            get {
                return ResourceManager.GetString("Cart_FailedToCreateCart_EasyShop", resourceCulture);
            }
        }
        
        public static string Cart_FailedToGetCartStatus_EasyShop {
            get {
                return ResourceManager.GetString("Cart_FailedToGetCartStatus_EasyShop", resourceCulture);
            }
        }
        
        public static string Cart_RemoveItemFailed_EasyShop {
            get {
                return ResourceManager.GetString("Cart_RemoveItemFailed_EasyShop", resourceCulture);
            }
        }
        
        public static string Cart_UpdateItemFailed_EasyShop {
            get {
                return ResourceManager.GetString("Cart_UpdateItemFailed_EasyShop", resourceCulture);
            }
        }
        
        public static string Catalogue_ItemNotFound {
            get {
                return ResourceManager.GetString("Catalogue_ItemNotFound", resourceCulture);
            }
        }
        
        public static string Notification_NotFound {
            get {
                return ResourceManager.GetString("Notification_NotFound", resourceCulture);
            }
        }
        
        public static string Notification_TitleRequired {
            get {
                return ResourceManager.GetString("Notification_TitleRequired", resourceCulture);
            }
        }
        
        public static string Notification_BodyRequired {
            get {
                return ResourceManager.GetString("Notification_BodyRequired", resourceCulture);
            }
        }
        
        public static string Notification_RecipientsCannotBeEmpty {
            get {
                return ResourceManager.GetString("Notification_RecipientsCannotBeEmpty", resourceCulture);
            }
        }
        
        public static string Notification_FailedToSend {
            get {
                return ResourceManager.GetString("Notification_FailedToSend", resourceCulture);
            }
        }
        
        public static string Auth_SessionIdRequired {
            get {
                return ResourceManager.GetString("Auth_SessionIdRequired", resourceCulture);
            }
        }
        
        public static string Auth_SessionNotFound {
            get {
                return ResourceManager.GetString("Auth_SessionNotFound", resourceCulture);
            }
        }
        
        public static string Notification_PushTokenMissing {
            get {
                return ResourceManager.GetString("Notification_PushTokenMissing", resourceCulture);
            }
        }
        
        public static string Email_VerificationSessionNotFound {
            get {
                return ResourceManager.GetString("Email_VerificationSessionNotFound", resourceCulture);
            }
        }
        
        public static string Email_VerificationSessionExpired {
            get {
                return ResourceManager.GetString("Email_VerificationSessionExpired", resourceCulture);
            }
        }
        
        public static string Email_InvalidVerificationCode {
            get {
                return ResourceManager.GetString("Email_InvalidVerificationCode", resourceCulture);
            }
        }
        
        public static string Email_VerificationSendFailed {
            get {
                return ResourceManager.GetString("Email_VerificationSendFailed", resourceCulture);
            }
        }
    }
}

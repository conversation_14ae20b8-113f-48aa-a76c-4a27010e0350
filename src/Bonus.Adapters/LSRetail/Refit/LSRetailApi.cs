using Bonus.Adapters.LSRetail.Requests;
using Refit;

namespace Bonus.Adapters.LSRetail.Refit;

public interface ILSRetailApi
{
    /// <summary>
    /// Custom Bonus API method
    /// </summary>
    [Post("/CreateWalletPass")]
    Task<ApiResponse<LSCreateWalletPassResponse>> CreateWalletPass(LSCreateWalletPassRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// Custom Bonus API method
    /// </summary>
    [Post("/AcceptTerms")]
    Task<ApiResponse<LSAcceptTermsResponse>> AcceptTerms(LSAcceptTermsRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCJson_SocialLogon.htm
    /// </summary>
    [Post("/SocialLogon")]
    Task<ApiResponse<LSSocialLogonResponse>> SocialLogon(LSSocialLogonRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCJson_Environment.htm
    /// </summary>
    [Post("/Environment")]
    Task<ApiResponse<LSEnvironmentResponse>> Environment(LSEnvironmentRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_OneListGetByCardId.htm
    /// </summary>
    [Post("/OneListGetByCardId")]
    Task<ApiResponse<LSGetOneListByCardIdResponse>> GetOneListByCardId(LSGetOneListByCardIdRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_OneListGetById.htm
    /// </summary>
    [Post("/OneListGetById")]
    Task<ApiResponse<LSGetOneListByIdResponse>> GetOneListById(LSGetOneListByIdRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_OneListDeleteById.htm
    /// </summary>
    [Post("/OneListDeleteById")]
    Task<ApiResponse<LSDeleteOneListByIdResponse>> DeleteOneListById(LSDeleteOneListByIdRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCJson_OneListLinking.htm
    /// </summary>
    [Post("/OneListLinking")]
    Task<ApiResponse<LSLinkOneListResponse>> LinkOneList(LSLinkOneListRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCJson_OneListItemModify.htm
    /// </summary>
    [Post("/OneListItemModify")]
    Task<ApiResponse<LSModifyOneListItemResponse>> ModifyOneListItem(LSModifyOneListItemRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_OneListSave.htm
    /// </summary>
    [Post("/OneListSave")]
    Task<ApiResponse<LSSaveOneListResponse>> SaveOneList(LSSaveOneListRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_HierarchyGet.htm
    /// </summary>
    [Post("/HierarchyGet")]
    Task<ApiResponse<LSGetHierarchyResponse>> GetHierarchy(LSGetHierarchyRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_ImageStreamGetById.htm
    /// </summary>
    [Get("/ImageStreamGetById")]
    Task<ApiResponse<Stream>> GetImageStreamById([Query] LSGetImageStreamByIdRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_ScanPayGoProfileGet.htm
    /// </summary>
    [Post("/ScanPayGoProfileGet")]
    Task<ApiResponse<LSScanPayGoProfileResponse>> GetScanPayGoProfile(LSScanPayGoProfileRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_ContactGetByCardId.htm
    /// </summary>
    [Post("/ContactGetByCardId")]
    Task<ApiResponse<LSGetContactGetByCardIdResponse>> GetContactByCardId(LSGetContactGetByCardIdRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCJson_ContactSearch.htm
    /// </summary>
    [Post("/ContactSearch")]
    Task<ApiResponse<LSContactSearchResponse>> ContactSearch(LSContactSearchRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCJson_ContactCreate.htm
    /// </summary>
    [Post("/ContactCreate")]
    Task<ApiResponse<LSContactCreateResponse>> CreateContact(LSContactCreateRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCJson_ContactUpdate.htm
    /// </summary>
    [Post("/ContactUpdate")]
    Task<ApiResponse<LSContactUpdateResponse>> UpdateContact(LSContactUpdateRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCJson_ContactBlock.htm
    /// </summary>
    [Post("/ContactBlock")]
    Task<ApiResponse<LSContactBlockResponse>> BlockContact(LSContactBlockRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_ProfilesGetAll.htm
    /// </summary>
    [Post("/ProfilesGetAll")]
    Task<ApiResponse<LSGetAllProfilesResponse>> GetAllProfiles(LSGetAllProfilesRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_SalesEntriesGetByCardId.htm
    /// </summary>
    [Post("/SalesEntriesGetByCardId")]
    Task<ApiResponse<LSGetSalesEntriesGetByCardIdResponse>> GetSalesEntriesByCardId(LSGetSalesEntriesGetByCardIdRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_SalesEntryGet.htm
    /// </summary>
    [Post("/SalesEntryGet")]
    Task<ApiResponse<LSGetSalesEntryResponse>> GetSalesEntry(LSGetSalesEntryRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCJson_ItemsSearch.htm
    /// </summary>
    [Post("/ItemsSearch")]
    Task<ApiResponse<LSGetItemsBySearchResponse>> GetItemsBySearch(LSGetItemsBySearchRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCJson_ItemsPage.htm
    /// </summary>
    [Post("/ItemsPage")]
    Task<ApiResponse<LSGetItemsByPageResponse>> GetItemsByPage(LSGetItemsByPageRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCJson_ItemGetById.htm
    /// </summary>
    [Post("/ItemGetById")]
    Task<ApiResponse<LSGetItemByIdResponse>> GetItemById(LSGetItemGetByIdRequest getByIdRequest, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCJson_ItemGetByBarcode.htm
    /// </summary>
    [Post("/ItemGetByBarcode")]
    Task<ApiResponse<LSGetItemByBarcodeResponse>> GetItemByBarcode(LSGetItemByBarcodeRequest byIdRequest, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_StoresGet.htm
    /// </summary>
    [Post("/StoresGet")]
    Task<ApiResponse<LSGetStoresResponse>> GetStores(LSGetStoresRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_StoreGetById.htm
    /// </summary>
    [Post("/StoreGetById")]
    Task<ApiResponse<LSGetStoreByIdResponse>> GetStoreById(LSGetStoreByIdRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_StoresGetByCoordinates.htm
    /// </summary>
    [Post("/StoresGetByCoordinates")]
    Task<ApiResponse<LSGetStoresByCoordinatesResponse>> GetStoresByCoordinates(LSGetStoresByCoordinatesRequest request, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_ReplEcommItems.htm
    /// </summary>
    [Post("/ReplEcommItems")]
    Task<ApiResponse<LSReplEcommItemsResponse>> ReplEcommItems(LSReplEcommItemsRequest replRequest, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_ReplEcommHierarchyNode.htm
    /// </summary>
    [Post("/ReplEcommHierarchyNode")]
    Task<ApiResponse<LSReplEcommHierarchyNodeResponse>> ReplEcommHierarchyNode(LSReplEcommHierarchyNodeRequest replRequest, CancellationToken cancellationToken);

    /// <summary>
    /// https://mobiledemo.lsretail.com/lsomnihelp/html/M_LSOmni_Service_IUCService_ReplEcommHierarchyLeaf.htm
    /// </summary>
    [Post("/ReplEcommHierarchyLeaf")]
    Task<ApiResponse<LSReplEcommHierarchyLeafResponse>> ReplEcommHierarchyLeaf(LSReplEcommHierarchyLeafRequest replRequest, CancellationToken cancellationToken);
}

using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSReplEcommHierarchyLeafRequest
{
    [JsonPropertyName("replRequest")]
    public required LSReplRequest ReplRequest { get; set; }
}

public class LSReplEcommHierarchyLeafResponse
{
    [JsonPropertyName("ReplEcommHierarchyLeafResult")]
    public LSReplEcommHierarchyLeafResult Result { get; set; }
}

public class LSReplEcommHierarchyLeafResult
{
    [JsonPropertyName("LastKey")]
    public string LastKey { get; set; } = string.Empty;

    [JsonPropertyName("MaxKey")]
    public string MaxKey { get; set; } = string.Empty;

    [JsonPropertyName("RecordsRemaining")]
    public int RecordsRemaining { get; set; }

    [JsonPropertyName("Leafs")]
    public List<LSReplEcommHierarchyLeaf> Leafs { get; set; } = [];
}

public class LSReplEcommHierarchyLeaf
{
    [JsonPropertyName("Id")]
    public string Id { get; set; }

    [JsonPropertyName("Description")]
    public string? Description { get; set; }

    [JsonPropertyName("HierarchyCode")]
    public string? HierarchyCode { get; set; }

    [JsonPropertyName("NodeId")]
    public string? NodeId { get; set; }

    [JsonPropertyName("ImageId")]
    public string? ImageId { get; set; }

    [JsonPropertyName("SortOrder")]
    public int SortOrder { get; set; }

    [JsonPropertyName("Type")]
    public int Type { get; set; }

    [JsonPropertyName("IsActive")]
    public bool IsActive { get; set; }

    [JsonPropertyName("IsDeleted")]
    public bool IsDeleted { get; set; }

    [JsonPropertyName("IsMemberClub")]
    public bool IsMemberClub { get; set; }

    [JsonPropertyName("ItemUOM")]
    public string? ItemUOM { get; set; }

    [JsonPropertyName("MemberValue")]
    public string? MemberValue { get; set; }

    [JsonPropertyName("DealPrice")]
    public decimal DealPrice { get; set; }

    [JsonPropertyName("Prepayment")]
    public decimal Prepayment { get; set; }

    [JsonPropertyName("ValidationPeriod")]
    public string? ValidationPeriod { get; set; }

    [JsonPropertyName("VendorSourcing")]
    public bool VendorSourcing { get; set; }
}

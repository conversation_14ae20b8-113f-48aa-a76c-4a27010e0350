using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Items;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetItemByBarcodeRequest
{
    [JsonPropertyName("barcode")]
    public required string Barcode { get; set; }

    [JsonPropertyName("storeId")]
    public string StoreId { get; set; } = string.Empty;
}

public class LSGetItemByBarcodeResponse
{
    [JsonPropertyName("ItemGetByBarcodeResult")]
    public LoyaltyItem Item { get; set; }
}

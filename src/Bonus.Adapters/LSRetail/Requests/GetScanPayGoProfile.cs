using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Setup;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSScanPayGoProfileRequest
{
    [JsonPropertyName("profileId")]
    public required string ProfileId { get; set; } = string.Empty;

    [JsonPropertyName("storeId")]
    public required string StoreId { get; set; } = string.Empty;
}

public class LSScanPayGoProfileResponse
{
    [JsonPropertyName("ScanPayGoProfileGetResult")]
    public ScanPayGoProfileGetResult ScanPayGoProfile { get; set; }
}

public class ScanPayGoProfileGetResult
{
    public required string Id { get; set; }

    public required FeatureFlag Flags { get; set; }
}

using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Members;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSContactCreateRequest
{
    [JsonPropertyName("contact")]
    public required MemberContact Contact { get; set; }

    [JsonPropertyName("doLogin")]
    public bool DoLogin { get; set; } = true;
}

public class LSContactCreateResponse
{
    [JsonPropertyName("ContactCreateResult")]
    public MemberContact? Contact { get; set; }
}

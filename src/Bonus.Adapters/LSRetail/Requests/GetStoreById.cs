using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Setup;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetStoreByIdRequest
{
    [JsonPropertyName("storeId")]
    public required string StoreId { get; set; }

    [JsonPropertyName("includeImages")]
    public bool IncludeImages { get; set; } = false;
}

public class LSGetStoreByIdResponse
{
    [JsonPropertyName("StoreGetByIdResult")]
    public Store Store { get; set; }
}

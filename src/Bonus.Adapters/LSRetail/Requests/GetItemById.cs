using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Items;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetItemGetByIdRequest
{
    [JsonPropertyName("itemId")]
    public required string ItemId { get; set; }

    [JsonPropertyName("storeId")]
    public string StoreId { get; set; } = string.Empty;
}

public class LSGetItemByIdResponse
{
    [JsonPropertyName("ItemGetByIdResult")]
    public LoyaltyItem? Item { get; set; }
}

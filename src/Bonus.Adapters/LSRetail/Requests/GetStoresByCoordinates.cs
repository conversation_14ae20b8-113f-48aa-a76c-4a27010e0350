using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Setup;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetStoresByCoordinatesRequest
{
    [JsonPropertyName("latitude")]
    public required decimal Latitude { get; set; }

    [JsonPropertyName("longitude")]
    public required decimal Longitude { get; set; }

    [JsonPropertyName("maxDistance")]
    public decimal MaxDistance { get; set; } = 150;
}

public class LSGetStoresByCoordinatesResponse
{
    [JsonPropertyName("StoresGetByCoordinatesResult")]
    public List<Store> Stores { get; set; } = [];
}

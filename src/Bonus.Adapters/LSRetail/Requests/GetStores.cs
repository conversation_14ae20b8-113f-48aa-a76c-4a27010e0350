using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Models.Setup;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetStoresRequest
{
    [JsonPropertyName("storeGetType")]
    public StoreGetType StoreGetType { get; set; } = StoreGetType.All;

    [JsonPropertyName("includeDetails")]
    public bool IncludeDetails { get; set; } = false;

    [JsonPropertyName("includeImages")]
    public bool IncludeImages { get; set; } = false;
}

public class LSGetStoresResponse
{
    [JsonPropertyName("StoresGetResult")]
    public List<Store> Stores { get; set; } = [];
}

using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Models.SalesEntries;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetSalesEntryRequest
{
    [JsonPropertyName("entryId")]
    public required string EntryId { get; set; }

    [JsonPropertyName("type")]
    public required DocumentIdType Type { get; set; }
}

public class LSGetSalesEntryResponse
{
    [JsonPropertyName("SalesEntryGetResult")]
    public SalesEntry? SalesEntry { get; set; }
}

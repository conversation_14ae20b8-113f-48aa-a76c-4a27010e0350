using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.SalesEntries;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetSalesEntriesGetByCardIdRequest
{
    [JsonPropertyName("cardId")]
    public required string CardId { get; set; }

    [JsonPropertyName("maxNumberOfTransactions")]
    public required int MaxNumberOfTransactions { get; set; }
}

public class LSGetSalesEntriesGetByCardIdResponse
{
    [JsonPropertyName("SalesEntriesGetByCardIdResult")]
    public List<SalesEntry> SalesEntries { get; set; } = [];
}

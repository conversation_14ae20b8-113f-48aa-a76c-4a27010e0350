using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Members;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSGetContactGetByCardIdRequest
{
    [JsonPropertyName("cardId")]
    public required string CardId { get; set; }

    [JsonPropertyName("numberOfTransReturned")]
    public required int NumberOfTransactionsReturned { get; set; }
}

public class LSGetContactGetByCardIdResponse
{
    [JsonPropertyName("ContactGetByCardIdResult")]
    public MemberContact? Contact { get; set; }
}

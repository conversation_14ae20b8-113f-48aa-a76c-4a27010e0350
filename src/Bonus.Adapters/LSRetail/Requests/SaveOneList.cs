using System.Text.Json.Serialization;
using Bonus.Adapters.LSRetail.Models.Baskets;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSSaveOneListRequest
{
    [JsonPropertyName("oneList")]
    public required OneList OneList { get; set; }

    public bool Calculate { get; set; } = false;
}

public class LSSaveOneListResponse
{
    [JsonPropertyName("OneListSaveResult")]
    public OneList OneList { get; set; }
}

using System.Net.Http.Headers;
using System.Text;
using Bonus.Shared.Configuration.Settings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Bonus.Adapters.Cloudinary;

public class CloudinaryHeaderHandler(ILogger<CloudinaryHeaderHandler> logger, IOptions<CloudinaryApiSettings> cloudinaryApiSettings) : DelegatingHandler
{

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var credentials = $"{cloudinaryApiSettings.Value.ApiKey}:{cloudinaryApiSettings.Value.ApiSecret}";
        var encodedCredentials = Convert.ToBase64String(Encoding.UTF8.GetBytes(credentials));
        request.Headers.Authorization = new AuthenticationHeaderValue("Basic", encodedCredentials);

        logger.LogInformation("Sending request to Cloudinary API: {Method} {Url}", request.Method, request.RequestUri);

        return await base.SendAsync(request, cancellationToken);
    }
}


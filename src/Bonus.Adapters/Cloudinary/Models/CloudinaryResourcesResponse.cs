using System.Text.Json.Serialization;

namespace Bonus.Adapters.Cloudinary.Models;

public class CloudinaryResourcesResponse
{
    [JsonPropertyName("resources")]
    public List<CloudinaryResource> Resources { get; set; } = new();

    [JsonPropertyName("next_cursor")]
    public string? NextCursor { get; set; }
}

public class CloudinaryResource
{
    [JsonPropertyName("asset_id")]
    public string AssetId { get; set; } = string.Empty;

    [JsonPropertyName("public_id")]
    public string PublicId { get; set; } = string.Empty;

    [JsonPropertyName("format")]
    public string Format { get; set; } = string.Empty;

    [JsonPropertyName("version")]
    public long Version { get; set; }

    [JsonPropertyName("resource_type")]
    public string ResourceType { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("created_at")]
    public DateTime CreatedAt { get; set; }

    [JsonPropertyName("bytes")]
    public long Bytes { get; set; }

    [JsonPropertyName("width")]
    public int Width { get; set; }

    [JsonPropertyName("height")]
    public int Height { get; set; }

    [JsonPropertyName("asset_folder")]
    public string AssetFolder { get; set; } = string.Empty;

    [JsonPropertyName("display_name")]
    public string DisplayName { get; set; } = string.Empty;

    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;

    [JsonPropertyName("secure_url")]
    public string SecureUrl { get; set; } = string.Empty;

    [JsonPropertyName("context")]
    public CloudinaryContext? Context { get; set; }

    [JsonPropertyName("last_updated")]
    public CloudinaryLastUpdated? LastUpdated { get; set; }
}

public class CloudinaryContext
{
    [JsonPropertyName("custom")]
    public CloudinaryCustomContext? Custom { get; set; }
}

public class CloudinaryCustomContext
{
    [JsonPropertyName("alt")]
    public string? Alt { get; set; } = string.Empty;

    [JsonPropertyName("caption")]
    public string? Caption { get; set; } = string.Empty;

    [JsonPropertyName("from")]
    public string? From { get; set; } = string.Empty;

    [JsonPropertyName("to")]
    public string? To { get; set; } = string.Empty;
}

public class CloudinaryLastUpdated
{
    [JsonPropertyName("context_updated_at")]
    public DateTime ContextUpdatedAt { get; set; }

    [JsonPropertyName("updated_at")]
    public DateTime UpdatedAt { get; set; }
}

using Bonus.Adapters.Cloudinary.Models;
using Refit;

namespace Bonus.Adapters.Cloudinary;

public interface ICloudinaryApi
{
    /// <summary>
    /// Gets the latest uploaded image from Cloudinary
    /// </summary>
    [Get("/v1_1/{cloudName}/resources/by_asset_folder")]
    Task<ApiResponse<CloudinaryResourcesResponse>> GetByAssetFolder(
        string cloudName,
        [Query, AliasAs("asset_folder")] string assetFolder = "posters",
        [Query] bool metadata = true,
        [Query, AliasAs("max_results")] int maxResults = 1,
        [Query] string direction = "desc",
        CancellationToken cancellationToken = default);

}


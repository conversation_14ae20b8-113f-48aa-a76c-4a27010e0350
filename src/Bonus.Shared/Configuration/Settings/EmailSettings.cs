using Bonus.Shared.Extensions;

namespace Bonus.Shared.Configuration.Settings;

public class EmailSettings : DbConfigOptionsBase
{
    public string SmtpHost { get; set; } = string.Empty;
    public int SmtpPort { get; set; }
    public string SmtpUsername { get; set; } = string.Empty;
    public string SmtpPassword { get; set; } = string.Empty;
    public bool EnableSsl { get; set; }
    public string FromEmail { get; set; } = string.Empty;
    public string FromName { get; set; } = string.Empty;
    public string VerificationSubject { get; set; } = string.Empty;
    public string VerificationTemplate { get; set; } = string.Empty;
}

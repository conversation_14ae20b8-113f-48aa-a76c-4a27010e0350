using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Services;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bonus.Core.Handlers.SalesEntries;

public class SyncSalesEntriesForUserCommand(
    BonusContext context,
    ILSRetailAdapter lsRetailAdapter,
    IBackgroundJobService backgroundJobService,
    ILogger<SyncSalesEntriesForUserCommand> logger
) : IRequestHandler<SyncSalesEntriesForUserRequest, SyncSalesEntriesForUserResponse>
{
    public async Task<SyncSalesEntriesForUserResponse> Handle(SyncSalesEntriesForUserRequest request, CancellationToken cancellationToken)
    {
        var user = await context.Users
            .Where(x => x.CardId == request.CardId)
            .Select(x => new
            {
                User = x,
                SalesEntries = x.SalesEntries.Select(y => y.EntryId).ToList()
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (user?.User is null)
        {
            logger.LogError("No user found in database for cardId {CardId} when syncing sales entries", request.CardId);
            throw new BonusException("User not found in database when syncing sales entries");
        }

        var lsRetailRequest = new LSGetSalesEntriesGetByCardIdRequest
        {
            CardId = request.CardId,
            MaxNumberOfTransactions = 500,
        };

        var salesEntriesResponse = await lsRetailAdapter.GetSalesEntriesByCardId(lsRetailRequest, cancellationToken);

        if (salesEntriesResponse.Success is false)
        {
            logger.LogError("Failed to sync sales entries for cardId {CardId}. LSRetail Error Code: {Error}", request.CardId, salesEntriesResponse.Error?.FaultCode);
            throw new BonusException($"Failed to sync sales entries for cardId {request.CardId}");
        }

        var salesEntries = salesEntriesResponse.Response?.SalesEntries.Select(x => new
        {
            Id = x.Id,
            PurchaseDate = x.DocumentRegTime.UtcDateTime,
            TotalAmount = x.TotalAmount,
            StoreName = x.StoreName,
            ReceiptNumber = x.ReceiptNumber
        }).ToList() ?? [];

        if (salesEntries.Any() is false)
        {
            return new();
        }

        var newSalesEntries = salesEntries.Where(x => user.SalesEntries.Contains(x.Id) is false).ToList();

        if (newSalesEntries.Any() is false)
        {
            return new();
        }

        foreach (var salesEntry in newSalesEntries)
        {
            var dbSalesEntry = new SalesEntry
            {
                EntryId = salesEntry.Id,
                PurchaseDate = salesEntry.PurchaseDate,
                TotalAmount = salesEntry.TotalAmount,
                StoreName = salesEntry.StoreName,
                ReceiptNumber = salesEntry.ReceiptNumber,
                User = user.User,
            };

            await context.SalesEntries.AddAsync(dbSalesEntry, cancellationToken);
        }

        await context.SaveChangesAsync();

        foreach (var salesEntry in newSalesEntries)
        {
            await backgroundJobService.SyncSalesEntry(salesEntry.Id);
        }

        return new();
    }
}

public class SyncSalesEntriesForUserRequest : IRequest<SyncSalesEntriesForUserResponse>
{
    public required string CardId { get; set; }
}

public class SyncSalesEntriesForUserResponse;

using Bonus.Core.Data;
using Bonus.Core.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Core.Handlers.SalesEntries;

public class SyncSalesEntriesCommand(
    BonusContext context,
    IBackgroundJobService backgroundJobService
) : IRequestHandler<SyncSalesEntriesRequest, SyncSalesEntriesResponse>
{
    public async Task<SyncSalesEntriesResponse> Handle(SyncSalesEntriesRequest request, CancellationToken cancellationToken)
    {
        var userCardIds = await context.Users.Select(x => x.CardId).ToListAsync(cancellationToken);

        foreach (var cardId in userCardIds)
        {
            await backgroundJobService.SyncSalesEntries(cardId);
        }

        return new();
    }
}

public class SyncSalesEntriesRequest : IRequest<SyncSalesEntriesResponse>;

public class SyncSalesEntriesResponse;

using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bonus.Core.Handlers.SalesEntries;

public class SyncSalesEntryCommand(
    BonusContext context,
    ILSRetailAdapter lsRetailAdapter,
    ILogger<SyncSalesEntryCommand> logger
) : IRequestHandler<SyncSalesEntryRequest, SyncSalesEntryResponse>
{
    public async Task<SyncSalesEntryResponse> Handle(SyncSalesEntryRequest request, CancellationToken cancellationToken)
    {
        var dbSalesEntry = await context.SalesEntries
            .Include(x => x.Items)
            .Where(x => x.EntryId == request.EntryId)
            .FirstOrDefaultAsync(cancellationToken);
        
        if (dbSalesEntry is not null && dbSalesEntry.Items.Any())
        {
            // If items exist, no need to sync again
            return new();
        }
        
        if (dbSalesEntry is null)
        {
            throw new BonusException("Sales entry must exist before syncing items");
        }

        var lsRetailRequest = new LSGetSalesEntryRequest()
        {
            EntryId = request.EntryId,
            Type = DocumentIdType.Receipt,
        };

        var salesEntryResponse = await lsRetailAdapter.GetSalesEntry(lsRetailRequest, cancellationToken);
        var lsSalesEntry = salesEntryResponse.Response?.SalesEntry;
        
        if (salesEntryResponse.Success is false || lsSalesEntry is null)
        {
            logger.LogError("Failed to get sales entry {EntryId} from LS Retail: {ErrorMessage}", request.EntryId, dbSalesEntry);
            throw new BonusException("Failed to get sales entry from LS Retail");
        }

        foreach (var line in lsSalesEntry.Lines)
        {
            var dbSalesEntryItem = new SalesEntryItem
            {
                LineId = line.Id,
                ItemId = line.ItemId,
                Name = line.ItemDescription,
                Quantity = line.Quantity,
                Price = line.Price,
                SalesEntry = dbSalesEntry!,
            };
            
            context.SalesEntryItems.Add(dbSalesEntryItem);
        }

        
        await context.SaveChangesAsync();
        
        return new();
    }
}

public class SyncSalesEntryRequest : IRequest<SyncSalesEntryResponse>
{
    public required string EntryId { get; set; }
}

public class SyncSalesEntryResponse;
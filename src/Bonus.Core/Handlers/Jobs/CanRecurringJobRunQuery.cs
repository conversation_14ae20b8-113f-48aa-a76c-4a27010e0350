using Bonus.Core.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Core.Handlers.Jobs;

public class CanRecurringJobRunQuery(BonusContext context)
    : IRequestHandler<CanRecurringJobRunRequest, CanRecurringJobRunResponse>
{
    public async Task<CanRecurringJobRunResponse> Handle(CanRecurringJobRunRequest request, CancellationToken cancellationToken)
    {
        var recurringJobData = await context.RecurringJobs
            .Where(x => x.JobName == request.Name)
            .Select(x => new
            {
                ShouldJobRun = x.IsEnabled
            })
            .FirstOrDefaultAsync(cancellationToken);

        return new CanRecurringJobRunResponse
        {
            ShouldJobRun = recurringJobData?.ShouldJobRun ?? false,
        };
    }
}

public class CanRecurringJobRunRequest : IRequest<CanRecurringJobRunResponse>
{
    public required string Name { get; set; }
}

public class CanRecurringJobRunResponse
{
    public bool ShouldJobRun { get; set; }
}

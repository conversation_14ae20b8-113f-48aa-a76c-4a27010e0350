using Bonus.Core.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bonus.Core.Handlers.Sync;

public class SyncCatalogueCommand(
    IHierarchySyncService hierarchySyncService,
    IProductSyncService productSyncService,
    IProductSearchService productSearchService,
    ILogger<SyncCatalogueCommand> logger) : IRequestHandler<SyncCatalogueRequest, SyncCatalogueResponse>
{
    public async Task<SyncCatalogueResponse> Handle(SyncCatalogueRequest request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Starting full catalogue synchronization");

        logger.LogInformation("Step 1/4: Syncing hierarchy nodes");
        await hierarchySyncService.SyncNodesAsync(cancellationToken);

        logger.LogInformation("Step 2/4: Syncing products");
        await productSyncService.SyncProductsAsync(cancellationToken);

        logger.LogInformation("Step 3/4: Syncing hierarchy leafs");
        await hierarchySyncService.SyncLeafsAsync(cancellationToken);

        logger.LogInformation("Step 4/4: Creating search index");
        await productSearchService.IndexAllItemsAsync(cancellationToken);

        logger.LogInformation("Completed full catalogue synchronization");

        return new SyncCatalogueResponse();
    }
}

public class SyncCatalogueRequest : IRequest<SyncCatalogueResponse>;

public class SyncCatalogueResponse;


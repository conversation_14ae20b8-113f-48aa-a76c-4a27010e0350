using System.Linq.Expressions;
using Bonus.Core.Data.Entities;
using Bonus.Core.Data.Entities.Search;
using Bonus.Shared.Data.Entities.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Metadata.Conventions;

namespace Bonus.Core.Data;

public partial class BonusContext : DbContext
{
    public BonusContext(DbContextOptions<BonusContext> options)
        : base(options)
    {
    }

    public Task<int> SaveChangesAsync()
    {
        return base.SaveChangesAsync(CancellationToken.None);
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        SetUniqueIndexes(modelBuilder);
        SetForeignKeys(modelBuilder);
        SetSoftDeleteQueryFilter(modelBuilder);
        SetOnDeleteConventionToRestrict(modelBuilder);
        SetPerformanceIndexes(modelBuilder);

        base.OnModelCreating(modelBuilder);
    }

    private void SetUniqueIndexes(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ProductSearchEmbedding>()
            .HasIndex(x => x.ItemId)
            .IsUnique();
    }

    private void SetForeignKeys(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Product>()
            .HasOne(x => x.HierarchyNode)
            .WithMany()
            .HasForeignKey(x => x.HierarchyNodeId)
            .HasPrincipalKey(x => x.Id);
    }

    private void SetPerformanceIndexes(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ShoppingListItem>()
            .HasIndex(x => new
            {
                x.ShoppingListId,
                x.ItemDescription
            })
            .HasDatabaseName("perf_shopping_list_items");

        modelBuilder.Entity<ShoppingListShare>()
            .HasIndex(x => new
            {
                x.ShareId,
                x.ShoppingListId,
                x.LinkedCardId
            })
            .HasDatabaseName("perf_shopping_list_shares");
    }


    private void SetSoftDeleteQueryFilter(ModelBuilder modelBuilder)
    {
        var softDeleteEntities = typeof(IArchivableEntity).Assembly.GetTypes()
            .Where(x => typeof(IArchivableEntity).IsAssignableFrom(x))
            .Where(x => x.IsClass)
            .Where(x => !x.IsAbstract);

        foreach (var softDeleteEntity in softDeleteEntities)
        {
            modelBuilder.Entity(softDeleteEntity)
                .HasQueryFilter(GenerateQueryFilterLambdaExpression(softDeleteEntity));
        }

        static LambdaExpression GenerateQueryFilterLambdaExpression(Type type)
        {
            // x =>
            var parameter = Expression.Parameter(type, "x");

            // null
            var falseConstant = Expression.Constant(null);

            // x.ArchiveDate
            var propertyAccess = Expression.PropertyOrField(parameter, nameof(IArchivableEntity.ArchivedTime));

            // e.ArchiveDate == null
            var equalExpression = Expression.Equal(propertyAccess, falseConstant);

            // x => e.ArchiveDate == null
            var lambda = Expression.Lambda(equalExpression, parameter);

            return lambda;
        }
    }

    private void SetOnDeleteConventionToRestrict(ModelBuilder modelBuilder)
    {
        foreach (var relationship in modelBuilder.Model.GetEntityTypes().SelectMany(e => e.GetForeignKeys()))
        {
            relationship.DeleteBehavior = DeleteBehavior.Restrict;
        }
    }

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        configurationBuilder.Conventions.Add(_ => new EnumsAsStringConvention());
    }

    private class EnumsAsStringConvention : IModelFinalizingConvention
    {
        public void ProcessModelFinalizing(IConventionModelBuilder modelBuilder, IConventionContext<IConventionModelBuilder> context)
        {
            var enumProperties = modelBuilder.Metadata.GetEntityTypes()
                .SelectMany(entityType => entityType.GetDeclaredProperties()
                    .Where(property => property.PropertyInfo?.PropertyType.IsEnum ?? false));

            foreach (var property in enumProperties)
            {
                property.Builder.HasConversion(typeof(string));
            }

            var nullableEnumProperties = modelBuilder.Metadata.GetEntityTypes()
                .SelectMany(entityType => entityType.GetDeclaredProperties()
                    .Where(property => property.PropertyInfo?.PropertyType.IsGenericType ?? false)
                    .Where(property => property.PropertyInfo?.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
                    .Where(property => Nullable.GetUnderlyingType(property.PropertyInfo!.PropertyType)!.IsEnum));

            foreach (var property in nullableEnumProperties)
            {
                property.Builder.HasConversion(typeof(string));
            }
        }
    }
}

using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bonus.Core.Data.Migrations
{
    /// <inheritdoc />
    public partial class ProductHierarchy : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {

            migrationBuilder.Sql(@"DELETE FROM products");

            migrationBuilder.DropPrimaryKey(
                name: "pk_products",
                table: "products");

            migrationBuilder.DropColumn(
                name: "id",
                table: "products");

            migrationBuilder.AddColumn<int>(
                name: "id",
                table: "products",
                type: "int",
                nullable: false);

            migrationBuilder.AddPrimaryKey(
                name: "pk_products",
                table: "products",
                column: "id");

            migrationBuilder.DropIndex(
                name: "ix_products_item_id",
                table: "products");

            migrationBuilder.DropColumn(
                name: "is_deleted",
                table: "products");

            migrationBuilder.DropColumn(
                name: "item_id",
                table: "products");

            migrationBuilder.AddColumn<DateTime>(
                name: "archived_time",
                table: "products",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "hierarchy_node_id",
                table: "products",
                type: "nvarchar(450)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "image_id",
                table: "products",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "last_hierarchy_sync_time",
                table: "products",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<int>(
                name: "sort_order",
                table: "products",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "hierarchy_nodes",
                columns: table => new
                {
                    id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    hierarchy_code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    parent_node = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    image_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    indentation = table.Column<int>(type: "int", nullable: false),
                    children_order = table.Column<int>(type: "int", nullable: false),
                    presentation_order = table.Column<int>(type: "int", nullable: false),
                    last_sync_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    archived_time = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hierarchy_nodes", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_products_hierarchy_node_id",
                table: "products",
                column: "hierarchy_node_id");

            migrationBuilder.AddForeignKey(
                name: "fk_products_hierarchy_nodes_hierarchy_node_id",
                table: "products",
                column: "hierarchy_node_id",
                principalTable: "hierarchy_nodes",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_products_hierarchy_nodes_hierarchy_node_id",
                table: "products");

            migrationBuilder.DropTable(
                name: "hierarchy_nodes");

            migrationBuilder.DropIndex(
                name: "ix_products_hierarchy_node_id",
                table: "products");

            migrationBuilder.DropColumn(
                name: "archived_time",
                table: "products");

            migrationBuilder.DropColumn(
                name: "hierarchy_node_id",
                table: "products");

            migrationBuilder.DropColumn(
                name: "image_id",
                table: "products");

            migrationBuilder.DropColumn(
                name: "last_hierarchy_sync_time",
                table: "products");

            migrationBuilder.DropColumn(
                name: "sort_order",
                table: "products");

            migrationBuilder.AlterColumn<int>(
                name: "id",
                table: "products",
                type: "int",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int")
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddColumn<bool>(
                name: "is_deleted",
                table: "products",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "item_id",
                table: "products",
                type: "nvarchar(450)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "ix_products_item_id",
                table: "products",
                column: "item_id",
                unique: true);
        }
    }
}

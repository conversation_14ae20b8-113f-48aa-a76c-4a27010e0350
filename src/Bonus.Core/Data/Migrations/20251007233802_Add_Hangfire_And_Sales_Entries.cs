using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bonus.Core.Data.Migrations
{
    /// <inheritdoc />
    public partial class Add_Hangfire_And_Sales_Entries : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "recurring_jobs",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    job_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    is_enabled = table.Column<bool>(type: "bit", nullable: false),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_recurring_jobs", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "sales_entries",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    user_id = table.Column<int>(type: "int", nullable: false),
                    entry_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    purchase_date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    total_amount = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    store_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    receipt_number = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sales_entries", x => x.id);
                    table.ForeignKey(
                        name: "fk_sales_entries_users_user_id",
                        column: x => x.user_id,
                        principalTable: "users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "sales_entry_items",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    sales_entry_id = table.Column<int>(type: "int", nullable: false),
                    line_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    item_id = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    quantity = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    price = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_sales_entry_items", x => x.id);
                    table.ForeignKey(
                        name: "fk_sales_entry_items_sales_entries_sales_entry_id",
                        column: x => x.sales_entry_id,
                        principalTable: "sales_entries",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "ix_sales_entries_user_id",
                table: "sales_entries",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "ix_sales_entry_items_sales_entry_id",
                table: "sales_entry_items",
                column: "sales_entry_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "recurring_jobs");

            migrationBuilder.DropTable(
                name: "sales_entry_items");

            migrationBuilder.DropTable(
                name: "sales_entries");
        }
    }
}

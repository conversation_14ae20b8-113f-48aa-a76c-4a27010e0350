using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class SalesEntryItem : BaseEntity
{
    public int SalesEntryId { get; set; }
    
    public required string LineId { get; set; }
    
    public required string ItemId { get; set; }
    
    public required string Name { get; set; }
    
    public decimal Quantity { get; set; }
    
    public decimal Price { get; set; }
    
    public SalesEntry SalesEntry { get; set; } = null!;
}
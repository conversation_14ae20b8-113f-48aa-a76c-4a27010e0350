using System.ComponentModel.DataAnnotations.Schema;
using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class Product : IArchivableEntity
{

    [DatabaseGenerated(DatabaseGeneratedOption.None)]
    public required int Id { get; set; }

    public string? Description { get; set; }

    public string? Details { get; set; }

    public decimal UnitPrice { get; set; }

    public string? ItemCategoryCode { get; set; }

    public string? ImageId { get; set; }

    public string? HierarchyNodeId { get; set; }

    public HierarchyNode? HierarchyNode { get; set; }

    public int? SortOrder { get; set; }

    public string? SalesUnitOfMeasure { get; set; }

    public DateTime LastSyncTime { get; set; }

    public DateTime LastHierarchySyncTime { get; set; }

    public DateTime CreatedTime { get; set; } = DateTime.UtcNow;

    public DateTime? ArchivedTime { get; set; }
}

using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class User : BaseArchivableEntity
{
    public required string MemberContactId { get; set; }

    public required string AccountId { get; set; }

    public required string CardId { get; set; }

    /// <summary>
    /// This can be nullable (only) because the Audkenni test user does not have it.
    /// </summary>
    public required string? AuthenticationId { get; set; }

    public required string? Ssn { get; set; }

    public bool IsEmailVerified { get; set; }

    public string? UserExternalNotificationId { get; set; }

    public List<SalesEntry> SalesEntries { get; set; } = [];

    public List<RefreshToken> RefreshTokens { get; set; } = [];

    public List<AuthenticationLog> AuthenticationLogs { get; set; } = [];
}

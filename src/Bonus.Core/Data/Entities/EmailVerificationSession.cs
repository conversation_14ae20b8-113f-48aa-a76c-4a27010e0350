using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class EmailVerificationSession : BaseEntity
{
    public required string Email { get; set; }
    
    public required string VerificationCode { get; set; }
    
    public required int UserId { get; set; }
    
    public DateTime ExpiresAt { get; set; }
    
    public bool IsCompleted { get; set; }
    
    public User User { get; set; } = null!;
}

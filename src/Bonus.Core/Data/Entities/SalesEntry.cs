using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class SalesEntry : BaseEntity
{
    public int UserId { get; set; }

    public required string EntryId { get; set; }

    public required DateTime PurchaseDate { get; set; }
    
    public required decimal TotalAmount { get; set; }
    
    public required string StoreName { get; set; }
    
    public required string ReceiptNumber { get; set; }
    
    public User User { get; set; } = null!;
    
    public List<SalesEntryItem> Items { get; set; } = [];
}
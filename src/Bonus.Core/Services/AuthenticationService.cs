using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Types.Common.Enums;
using Bonus.Shared.Configuration.Settings;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace Bonus.Core.Services;

public class AuthenticationService(BonusContext bonusContext, IOptions<JwtSettings> jwtOptions, ISystemTime systemTime) : IAuthenticationService
{
    private readonly JwtSettings _jwtSettings = jwtOptions.Value;

    public async Task<LoginData> CreateToken(int userId, string cardId, bool termsAccepted, AuthenticationLog authenticationLog)
    {
        var claims = new List<Claim>
        {
            new(BonusUserClaimTypes.UserId, userId.ToString()),
            new(BonusUserClaimTypes.CardId, cardId),
            new(BonusUserClaimTypes.DeviceId, authenticationLog.DeviceId!),
            new(BonusUserClaimTypes.TermsAccepted, termsAccepted.ToString()),
        };

        var token = GenerateJwtToken(claims);
        var refreshToken = GenerateRefreshToken(userId, authenticationLog);

        bonusContext.RefreshTokens.Add(refreshToken);

        return new LoginData(token, refreshToken.Token);
    }

    private string GenerateJwtToken(IEnumerable<Claim> claims)
    {
        var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Secret));
        var signingCredentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);
        var validTo = systemTime.UtcNow.AddMinutes(_jwtSettings.ExpiryMinutes);

        var jwtSecurityToken = new JwtSecurityToken(
            issuer: _jwtSettings.Issuer,
            audience: _jwtSettings.Audience,
            claims: claims,
            expires: validTo,
            signingCredentials: signingCredentials);

        return new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
    }

    private RefreshToken GenerateRefreshToken(int userId, AuthenticationLog authenticationLog)
    {
        return new RefreshToken
        {
            UserId = userId,
            Token = Guid.NewGuid().ToString(),
            ValidUntil = systemTime.UtcNow.AddMinutes(_jwtSettings.RefreshTokenExpiryMinutes),
            AuthenticationLog = authenticationLog,
        };
    }

    public async Task<AuthenticationLog> CreateAuthenticationLog(string credentialValue, CredentialType credentialType, int? userId, string? errorMessage, string ipAddress, string? manufacturer, string? modelName, string? deviceId, string? appVersion, string? osName, string? osVersion, int? hagarIdSessionId = null)
    {
        var authenticationLog = new AuthenticationLog
        {
            CredentialValue = credentialValue,
            CredentialType = credentialType,
            UserId = userId,
            ErrorMessage = errorMessage,
            IsSuccess = errorMessage.HasValue() is false,
            IpAddress = ipAddress,
            DeviceManufacturer = manufacturer,
            DeviceModelName = modelName,
            DeviceId = deviceId,
            AppVersion = appVersion,
            OperatingSystem = osName,
            OperatingSystemVersion = osVersion,
            HagarIdSessionId = hagarIdSessionId
        };

        bonusContext.AuthenticationLogs.Add(authenticationLog);

        return authenticationLog;
    }
}

public interface IAuthenticationService
{
    Task<LoginData> CreateToken(int userId, string cardId, bool termsAccepted, AuthenticationLog authenticationLog);

    Task<AuthenticationLog> CreateAuthenticationLog(string credentialValue, CredentialType credentialType, int? userId, string? errorMessage, string ipAddress, string? manufacturer, string? modelName, string? deviceId, string? appVersion, string? osName, string? osVersion, int? hagarIdSessionId = null);
}

public record LoginData(string Token, string RefreshToken);

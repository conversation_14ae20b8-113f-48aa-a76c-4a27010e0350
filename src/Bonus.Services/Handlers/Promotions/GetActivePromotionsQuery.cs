using Bonus.Adapters.Cloudinary;
using Bonus.Shared.Configuration.Settings;
using MediatR;
using Microsoft.Extensions.Options;

namespace Bonus.Services.Handlers.Promotions;

public class GetActivePromotionsQuery(ICloudinaryApi cloudinaryApi, IOptions<CloudinaryApiSettings> cloudinarySettings)
    : IRequestHandler<GetActivePromotionsRequest, GetActivePromotionsResponse>
{
    private const int MaxImageAgeDays = 7;

    public async Task<GetActivePromotionsResponse> Handle(GetActivePromotionsRequest request, CancellationToken cancellationToken)
    {
        var settings = cloudinarySettings.Value;

        var response = await cloudinaryApi.GetByAssetFolder(
            settings.CloudName,
            assetFolder: "posters",
            maxResults: 5,
            direction: "desc",
            cancellationToken: cancellationToken
            );

        if (!response.IsSuccessStatusCode || !(response.Content?.Resources.Any() ?? false))
        {
            return new GetActivePromotionsResponse
            {
                Promotions = [],
            };
        }

        var today = DateTime.UtcNow.Date;

        var promos = response.Content.Resources
            .Where(x => DateTime.TryParse(x.Context?.Custom?.From, out var fromDate) && fromDate.Date <= today)
            .Where(x => DateTime.TryParse(x.Context?.Custom?.To, out var toDate) && toDate.Date >= today)
            .OrderByDescending(x => x.CreatedAt)
            .Select(x => new Promotion
            {
                Title = x.Context?.Custom?.Caption ?? x.DisplayName,
                Description = x.Context?.Custom?.Alt ?? string.Empty,
                Image = x.SecureUrl,
                StoreSpecific = false,
                ValidFrom = DateTime.TryParse(x.Context?.Custom?.From, out var f) ? f : x.CreatedAt,
                ValidUntil = DateTime.TryParse(x.Context?.Custom?.To, out var t) ? t : x.CreatedAt.AddDays(MaxImageAgeDays)
            })
            .ToList();

        return new GetActivePromotionsResponse
        {
            Promotions = promos
        };
    }
}

public class GetActivePromotionsRequest : IRequest<GetActivePromotionsResponse>
{
    public string? StoreId { get; set; }
}

public class GetActivePromotionsResponse
{
    public required List<Promotion> Promotions { get; set; }
}

public class Promotion
{
    public required string Title { get; set; }

    public required string? Description { get; set; }

    public required string Image { get; set; }

    public required bool StoreSpecific { get; set; }

    public required DateTime ValidFrom { get; set; }

    public required DateTime? ValidUntil { get; set; }
}

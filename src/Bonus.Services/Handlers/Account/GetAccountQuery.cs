using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Handlers.Account;

public class GetAccountQuery(ILSRetailAdapter lsRetailAdapter, IBonusUser bonusUser)
    : IRequestHandler<GetAccountRequest, GetAccountResponse>
{
    public async Task<GetAccountResponse> Handle(GetAccountRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        var contactRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = bonusUser.CardId,
            NumberOfTransactionsReturned = 0,
        };

        var contactResponse = await lsRetailAdapter.GetContactByCardId(contactRequest, cancellationToken);

        if (contactResponse.Success is false || contactResponse.Response?.Contact is null)
        {
            throw new BonusException(nameof(Translations.Account_ContactNotFound));
        }

        var contact = contactResponse.Response!.Contact;
        var phoneNumber = contact.Addresses
            .Select(a => a.PhoneNumber)
            .FirstOrDefault(p => !string.IsNullOrEmpty(p));

        return new GetAccountResponse
        {
            UserId = bonusUser.UserId,
            FirstName = contact.FirstName!,
            LastName = contact.LastName!,
            Email = bonusUser.EmailVerified ? contact.Email : null,
            PhoneNumber = phoneNumber,
            CardId = bonusUser.CardId
        };
    }
}

public class GetAccountRequest : IRequest<GetAccountResponse>
{
}

public class GetAccountResponse
{
    public int UserId { get; set; }

    public string FirstName { get; set; } = string.Empty;

    public string LastName { get; set; } = string.Empty;

    public string? Email { get; set; } = string.Empty;

    public string? PhoneNumber { get; set; } = string.Empty;

    public string CardId { get; set; } = string.Empty;
}

using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Resources;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Account;

public class GetAccountQuery(ILSRetailAdapter lsRetailAdapter, IBonusUser bonusUser, BonusContext dbContext)
    : IRequestHandler<GetAccountRequest, GetAccountResponse>
{
    public async Task<GetAccountResponse> Handle(GetAccountRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        var contactRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = bonusUser.CardId,
            NumberOfTransactionsReturned = 0,
        };

        var contactResponse = await lsRetailAdapter.GetContactByCardId(contactRequest, cancellationToken);

        if (contactResponse.Success is false || contactResponse.Response?.Contact is null)
        {
            throw new BonusException(nameof(Translations.Account_ContactNotFound));
        }

        var contact = contactResponse.Response!.Contact;
        var phoneNumber = contact.Addresses
            .Select(a => a.PhoneNumber)
            .FirstOrDefault(p => !string.IsNullOrEmpty(p));

        var user = await dbContext.Users
            .Where(u => u.Id == bonusUser.UserId)
            .FirstAsync(cancellationToken);

        return new GetAccountResponse
        {
            UserId = bonusUser.UserId,
            FirstName = contact.FirstName!,
            LastName = contact.LastName!,
            Email = user.IsEmailVerified ? contact.Email : null,
            PhoneNumber = phoneNumber,
            CardId = bonusUser.CardId
        };
    }
}

public class GetAccountRequest : IRequest<GetAccountResponse>
{
}

public class GetAccountResponse
{
    public int UserId { get; set; }

    public string FirstName { get; set; } = string.Empty;

    public string LastName { get; set; } = string.Empty;

    public string? Email { get; set; } = string.Empty;

    public string? PhoneNumber { get; set; } = string.Empty;

    public string CardId { get; set; } = string.Empty;
}

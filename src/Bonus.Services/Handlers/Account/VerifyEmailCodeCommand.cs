using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Core.Services;
using Bonus.Resources;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Handlers.Account;

public class VerifyEmailCodeCommand(
    BonusContext bonusContext,
    ILSRetailAdapter lsRetailAdapter,
    IBonusUser bonusUser,
    ISystemTime systemTime,
    ILogger<VerifyEmailCodeCommand> logger)
    : IRequestHandler<VerifyEmailCodeRequest, VerifyEmailCodeResponse>
{
    public async Task<VerifyEmailCodeResponse> Handle(VerifyEmailCodeRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        // Find the email verification session
        var session = await bonusContext.EmailVerificationSessions
            .Where(x => x.Id == request.SessionId)
            .Where(x => x.UserId == bonusUser.UserId)
            .Where(x => !x.IsCompleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (session == null)
        {
            logger.LogWarning("Email verification session {SessionId} not found for user {UserId}", request.SessionId, bonusUser.UserId);
            throw new BonusException(nameof(Translations.Email_VerificationSessionNotFound));
        }

        // Check if session has expired
        if (session.ExpiresAt < systemTime.UtcNow)
        {
            logger.LogWarning("Email verification session {SessionId} has expired for user {UserId}", request.SessionId, bonusUser.UserId);
            throw new BonusException(nameof(Translations.Email_VerificationSessionExpired));
        }

        // Verify the code
        if (session.VerificationCode != request.VerificationCode)
        {
            logger.LogWarning("Invalid verification code provided for session {SessionId} by user {UserId}", request.SessionId, bonusUser.UserId);

            await bonusContext.SaveChangesAsync(cancellationToken);
            throw new BonusException(nameof(Translations.Email_InvalidVerificationCode));
        }

        try
        {
            // Check if email is already in use by another user
            var searchRequest = new LSContactSearchRequest
            {
                SearchType = ContactSearchType.Email,
                Search = session.Email,
                MaxNumberOfRowsReturned = 1
            };

            var searchResponse = await lsRetailAdapter.ContactSearch(searchRequest, cancellationToken);

            if (searchResponse.Success is false || searchResponse.Response!.Contacts.Any(x => x.Cards.Any(card => card.Id != bonusUser.CardId)))
            {
                throw new BonusException(nameof(Translations.Account_UpdateEmailFailed));
            }

            // Get current contact
            var contactRequest = new LSGetContactGetByCardIdRequest
            {
                CardId = bonusUser.CardId,
                NumberOfTransactionsReturned = 0,
            };

            var contactResponse = await lsRetailAdapter.GetContactByCardId(contactRequest, cancellationToken);

            if (contactResponse.Response?.Contact is null)
            {
                throw new BonusException(nameof(Translations.Account_ContactNotFound));
            }

            // Update contact email
            var contact = contactResponse.Response!.Contact;
            contact.Email = session.Email;

            var updateRequest = new LSContactUpdateRequest
            {
                Contact = contact,
                GetContact = true
            };

            var updateResponse = await lsRetailAdapter.UpdateContact(updateRequest, cancellationToken);

            if (updateResponse.Response?.Contact is null)
            {
                throw new BonusException(nameof(Translations.Account_UpdateEmailFailed));
            }

            // Mark session as completed
            session.IsCompleted = true;

            await bonusContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Email verification completed successfully for user {UserId} with email {Email}", bonusUser.UserId, session.Email);

            return new VerifyEmailCodeResponse();
        }
        catch (Exception ex) when (ex is not BonusException)
        {
            logger.LogError(ex, "Failed to complete email verification for user {UserId} with email {Email}", bonusUser.UserId, session.Email);
            throw new BonusException(nameof(Translations.Account_UpdateEmailFailed));
        }
    }
}

public class VerifyEmailCodeRequest : IRequest<VerifyEmailCodeResponse>
{
    public int SessionId { get; set; }
    public required string VerificationCode { get; set; }
}

public class VerifyEmailCodeResponse
{
}

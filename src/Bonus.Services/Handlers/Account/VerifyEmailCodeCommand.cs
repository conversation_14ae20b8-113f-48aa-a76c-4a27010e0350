using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Core.Services;
using Bonus.Resources;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Handlers.Account;

public class VerifyEmailCodeCommand(
    BonusContext bonusContext,
    ILSRetailAdapter lsRetailAdapter,
    IBonusUser bonusUser,
    ISystemTime systemTime,
    ILogger<VerifyEmailCodeCommand> logger)
    : IRequestHandler<VerifyEmailCodeRequest, VerifyEmailCodeResponse>
{
    public async Task<VerifyEmailCodeResponse> Handle(VerifyEmailCodeRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        var session = await bonusContext.EmailVerificationSessions
            .Where(x => x.UserId == bonusUser.UserId)
            .Where(x => !x.IsCompleted)
            .FirstOrDefaultAsync(cancellationToken);

        if (session == null)
        {
            logger.LogWarning("Email verification not found for user {UserId}", bonusUser.UserId);
            throw new BonusException(nameof(Translations.Email_VerificationSessionNotFound));
        }

        if (session.ExpiresAt < systemTime.UtcNow)
        {
            logger.LogWarning("Email verification  has expired for user {UserId}", bonusUser.UserId);
            throw new BonusException(nameof(Translations.Email_VerificationSessionExpired));
        }

        if (session.VerificationCode != request.VerificationCode)
        {
            logger.LogWarning("Invalid verification code provided by user {UserId}", bonusUser.UserId);

            await bonusContext.SaveChangesAsync(cancellationToken);
            throw new BonusException(nameof(Translations.Email_InvalidVerificationCode));
        }

        try
        {
            // Check if email is already in use by another user
            var searchRequest = new LSContactSearchRequest
            {
                SearchType = ContactSearchType.Email,
                Search = session.Email,
                MaxNumberOfRowsReturned = 1
            };

            var searchResponse = await lsRetailAdapter.ContactSearch(searchRequest, cancellationToken);

            if (searchResponse.Success is false || searchResponse.Response!.Contacts.Any(x => x.Cards.Any(card => card.Id != bonusUser.CardId)))
            {
                throw new BonusException(nameof(Translations.Account_UpdateEmailFailed));
            }

            var contactRequest = new LSGetContactGetByCardIdRequest
            {
                CardId = bonusUser.CardId,
                NumberOfTransactionsReturned = 0,
            };

            var contactResponse = await lsRetailAdapter.GetContactByCardId(contactRequest, cancellationToken);

            if (contactResponse.Response?.Contact is null)
            {
                throw new BonusException(nameof(Translations.Account_ContactNotFound));
            }

            var contact = contactResponse.Response!.Contact;
            contact.Email = session.Email;

            var updateRequest = new LSContactUpdateRequest
            {
                Contact = contact,
                GetContact = true
            };

            var updateResponse = await lsRetailAdapter.UpdateContact(updateRequest, cancellationToken);

            if (updateResponse.Response?.Contact is null)
            {
                throw new BonusException(nameof(Translations.Account_UpdateEmailFailed));
            }

            session.IsCompleted = true;

            var user = await bonusContext.Users
                .Where(x => x.Id == bonusUser.UserId)
                .FirstAsync(cancellationToken);

            user.IsEmailVerified = true;

            await bonusContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Email verification completed successfully for user {UserId} with email {Email}", bonusUser.UserId, session.Email);

            return new VerifyEmailCodeResponse();
        }
        catch (Exception ex) when (ex is not BonusException)
        {
            logger.LogError(ex, "Failed to complete email verification for user {UserId} with email {Email}", bonusUser.UserId, session.Email);
            throw new BonusException(nameof(Translations.Account_UpdateEmailFailed));
        }
    }
}

public class VerifyEmailCodeRequest : IRequest<VerifyEmailCodeResponse>
{
    public required string VerificationCode { get; set; }
}

public class VerifyEmailCodeResponse
{
}

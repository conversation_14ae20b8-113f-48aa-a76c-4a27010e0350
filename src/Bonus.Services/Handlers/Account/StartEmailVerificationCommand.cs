using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Services;
using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Handlers.Account;

public class StartEmailVerificationCommand(
    BonusContext bonusContext,
    IEmailService emailService,
    IBonusUser bonusUser,
    ISystemTime systemTime,
    ILogger<StartEmailVerificationCommand> logger)
    : IRequestHandler<StartEmailVerificationRequest, StartEmailVerificationResponse>
{
    public async Task<StartEmailVerificationResponse> Handle(StartEmailVerificationRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        // Generate 6-digit verification code
        var verificationCode = Random.Shared.Next(100000, 999999).ToString();

        // Create email verification session
        var session = new EmailVerificationSession
        {
            Email = request.Email,
            VerificationCode = verificationCode,
            UserId = bonusUser.UserId,
            ExpiresAt = systemTime.UtcNow.AddMinutes(15), // 15 minutes expiry
            IsCompleted = false
        };

        bonusContext.EmailVerificationSessions.Add(session);

        try
        {
            // Send verification email
            await emailService.SendVerificationEmailAsync(request.Email, verificationCode, cancellationToken);

            await bonusContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Email verification started for user {UserId} with email {Email}", bonusUser.UserId, request.Email);

            return new StartEmailVerificationResponse
            {
                SessionId = session.Id,
                ExpiresAt = session.ExpiresAt
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to start email verification for user {UserId} with email {Email}", bonusUser.UserId, request.Email);
            throw new BonusException(nameof(Translations.Email_VerificationSendFailed));
        }
    }
}

public class StartEmailVerificationRequest : IRequest<StartEmailVerificationResponse>
{
    public required string Email { get; set; }
}

public class StartEmailVerificationResponse
{
    public int SessionId { get; set; }
    public DateTime ExpiresAt { get; set; }
}

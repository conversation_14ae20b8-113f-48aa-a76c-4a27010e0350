using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Services;
using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Handlers.Account;

public class UpdateEmailCommand(
    BonusContext bonusContext,
    IEmailService emailService,
    IBonusUser bonusUser,
    ISystemTime systemTime,
    ILogger<UpdateEmailCommand> logger)
    : IRequestHandler<UpdateEmailRequest, UpdateEmailResponse>
{

    public async Task<UpdateEmailResponse> Handle(UpdateEmailRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        var existingSessions = await bonusContext.EmailVerificationSessions
            .Where(x => x.UserId == bonusUser.UserId && !x.IsCompleted)
            .ToListAsync(cancellationToken);

        foreach (var existingSession in existingSessions)
        {
            existingSession.IsCompleted = true;
        }

        var verificationCode = Random.Shared.Next(100000, 999999).ToString();

        var session = new EmailVerificationSession
        {
            Email = request.Email,
            VerificationCode = verificationCode,
            UserId = bonusUser.UserId,
            ExpiresAt = systemTime.UtcNow.AddMinutes(15),
            IsCompleted = false
        };

        bonusContext.EmailVerificationSessions.Add(session);

        try
        {
            await emailService.SendVerificationEmailAsync(request.Email, verificationCode, cancellationToken);

            await bonusContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Email verification started for user {UserId} with email {Email}", bonusUser.UserId, request.Email);

            return new UpdateEmailResponse
            {
                ExpiresAt = session.ExpiresAt
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to start email verification for user {UserId} with email {Email}", bonusUser.UserId, request.Email);
            throw new BonusException(nameof(Translations.Email_VerificationSendFailed));
        }
    }
}

public class UpdateEmailRequest : IRequest<UpdateEmailResponse>
{
    public required string Email { get; set; }
}

public class UpdateEmailResponse
{
    public DateTime ExpiresAt { get; set; }
}

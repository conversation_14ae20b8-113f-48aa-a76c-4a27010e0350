using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Resources;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Account;

public class UpdateEmailCommand(ILSRetailAdapter lsRetailAdapter, IBonusUser bonusUser)
    : IRequestHandler<UpdateEmailRequest, UpdateEmailResponse>
{
    public async Task<UpdateEmailResponse> Handle(UpdateEmailRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        var contactRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = bonusUser.CardId,
            NumberOfTransactionsReturned = 0,
        };

        var contactResponse = await lsRetailAdapter.GetContactByCardId(contactRequest, cancellationToken);

        if (contactResponse.Response?.Contact is null)
        {
            throw new BonusException(nameof(Translations.Account_ContactNotFound));
        }

        var searchRequest = new LSContactSearchRequest
        {
            SearchType = ContactSearchType.Email,
            Search = request.Email,
            MaxNumberOfRowsReturned = 1
        };

        var searchResponse = await lsRetailAdapter.ContactSearch(searchRequest, cancellationToken);

        if (searchResponse.Success is false || searchResponse.Response!.Contacts.Any(x => x.Cards.Any(card => card.Id != bonusUser.CardId)))
        {
            throw new BonusException(nameof(Translations.Account_UpdateEmailFailed));
        }

        var contact = contactResponse.Response!.Contact;
        contact.Email = request.Email;

        var updateRequest = new LSContactUpdateRequest
        {
            Contact = contact,
            GetContact = true
        };

        var updateResponse = await lsRetailAdapter.UpdateContact(updateRequest, cancellationToken);

        if (updateResponse.Response?.Contact is null)
        {
            throw new BonusException(nameof(Translations.Account_UpdateEmailFailed));
        }

        return new UpdateEmailResponse
        {
            Email = updateResponse.Response!.Contact.Email!
        };
    }
}

public class UpdateEmailRequest : IRequest<UpdateEmailResponse>
{
    public required string Email { get; set; }
}

public class UpdateEmailResponse
{
    public required string Email { get; set; }
}

using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Resources;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.Account;

public class UpdatePhoneCommand(ILSRetailAdapter lsRetailAdapter, IBonusUser bonusUser)
    : IRequestHandler<UpdatePhoneRequest, UpdatePhoneResponse>
{
    public async Task<UpdatePhoneResponse> Handle(UpdatePhoneRequest request, CancellationToken cancellationToken)
    {
        var contactRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = bonusUser.CardId,
            NumberOfTransactionsReturned = 0,
        };

        var contactResponse = await lsRetailAdapter.GetContactByCardId(contactRequest, cancellationToken);
        if (contactResponse.Response?.Contact is null)
        {
            throw new BonusException(nameof(Translations.Account_ContactNotFound));
        }

        var contact = contactResponse.Response!.Contact;

        if (!contact.Addresses.Any())
        {
            throw new BonusException(nameof(Translations.Account_NoAddressesFound));
        }

        // TODO: Validate phone number format? Should we always prefix with +354 or strip it or something?
        var searchRequest = new LSContactSearchRequest
        {
            SearchType = ContactSearchType.PhoneNumber,
            Search = request.PhoneNumber,
            MaxNumberOfRowsReturned = 1
        };

        var searchResponse = await lsRetailAdapter.ContactSearch(searchRequest, cancellationToken);

        if (searchResponse.Response?.Contacts.Any(x => x.Cards.Any(card => card.Id != bonusUser.CardId)) == true)
        {
            throw new BonusException(nameof(Translations.Account_UpdatePhoneFailed));
        }

        contact.Addresses.First().PhoneNumber = request.PhoneNumber;

        var updateRequest = new LSContactUpdateRequest
        {
            Contact = contact,
            GetContact = true
        };

        var updateResponse = await lsRetailAdapter.UpdateContact(updateRequest, cancellationToken);

        if (updateResponse.Response?.Contact == null)
        {
            throw new BonusException(nameof(Translations.Account_UpdatePhoneFailed));
        }

        var phoneNumber = updateResponse.Response!.Contact.Addresses.FirstOrDefault()?.PhoneNumber
            ?? throw new BonusException(nameof(Translations.Account_PhoneNotUpdated));

        return new UpdatePhoneResponse
        {
            PhoneNumber = phoneNumber
        };
    }
}

public class UpdatePhoneRequest : IRequest<UpdatePhoneResponse>
{
    public required string PhoneNumber { get; set; }
}

public class UpdatePhoneResponse
{
    public required string PhoneNumber { get; set; }
}

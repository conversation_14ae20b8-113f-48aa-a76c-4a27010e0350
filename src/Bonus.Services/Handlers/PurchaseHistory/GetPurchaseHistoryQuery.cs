using Bonus.Core.Data;
using Bonus.Services.Services;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.PurchaseHistory;

public class GetPurchaseHistoryQuery(BonusContext bonusContext, IImageUrlService imageUrlService, IBonusUser bonusUser)
    : IRequestHandler<GetPurchaseHistoryRequest, GetPurchaseHistoryResponse>
{
    public async Task<GetPurchaseHistoryResponse> Handle(GetPurchaseHistoryRequest request, CancellationToken cancellationToken)
    {
        var purchaseHistory = await bonusContext.SalesEntries
            .Where(x => x.UserId == bonusUser.UserId)
            .Select(x => new ResultPurchaseHistory
            {
                Id = x.Id,
                PurchaseDate = x.PurchaseDate,
                TotalAmount = x.TotalAmount,
                Title = x.StoreName,
                QrCode = x.ReceiptNumber, // TODO: implement QR code generation,
                Items = x.Items.Select(y => new PurchaseHistoryItemDetail
                {
                    Id = y.Id,
                    ItemId = y.ItemId,
                    Name = y.Name,
                    Quantity = y.Quantity,
                    Price = y.Price,
                }).ToList()
            })
            .Take(request.MaxNumberOfTransactions ?? 100)
            .ToListAsync(cancellationToken);

        foreach (var item in purchaseHistory.SelectMany(x => x.Items))
        {
            item.ImageUrl = imageUrlService.CreateImageUrl(item.ItemId);
        }

        return new GetPurchaseHistoryResponse
        {
            PurchaseHistory = purchaseHistory
        };
    }
}

public class GetPurchaseHistoryRequest : IRequest<GetPurchaseHistoryResponse>
{
    public int? MaxNumberOfTransactions { get; set; }
}

public class GetPurchaseHistoryResponse
{
    public required List<ResultPurchaseHistory> PurchaseHistory { get; set; } = [];
}

public class ResultPurchaseHistory
{
    public required int Id { get; set; }

    public required DateTime PurchaseDate { get; set; }

    public required decimal TotalAmount { get; set; }

    public required string Title { get; set; }

    public required string QrCode { get; set; }

    public required List<PurchaseHistoryItemDetail> Items { get; set; } = [];
}

public class PurchaseHistoryItemDetail
{
    public required int Id { get; set; }

    public required string Name { get; set; }

    public required string ItemId { get; set; }

    public required decimal Quantity { get; set; }

    public required decimal Price { get; set; }

    public string ImageUrl { get; set; }
}

using Bonus.Core.Data;
using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.PurchaseHistory;

public class GetPurchaseHistoryDetailsQuery(BonusContext bonusContext, IBonusUser bonusUser, IImageUrlService imageUrlService)
    : IRequestHandler<GetPurchaseHistoryDetailsRequest, GetPurchaseHistoryDetailsResponse>
{
    public async Task<GetPurchaseHistoryDetailsResponse> Handle(GetPurchaseHistoryDetailsRequest request, CancellationToken cancellationToken)
    {
        var salesEntry = await bonusContext.SalesEntries
            .Where(x => x.UserId == bonusUser.UserId)
            .Where(x => x.Id == request.Id)
            .Select(x => new ResultPurchaseHistory
            {
                Id = x.Id,
                PurchaseDate = x.PurchaseDate,
                TotalAmount = x.TotalAmount,
                Title = x.StoreName,
                QrCode = x.ReceiptNumber, // TODO: implement QR code generation,
                Items = x.Items.Select(y => new PurchaseHistoryItemDetail
                {
                    Id = y.Id,
                    ItemId = y.ItemId,
                    Name = y.Name,
                    Quantity = y.Quantity,
                    Price = y.Price,
                }).ToList()
            })
            .FirstOrDefaultAsync(cancellationToken);

        if (salesEntry is null)
        {
            throw new BonusException(nameof(Translations.Communication_UnexpectedError));
        }

        foreach (var item in salesEntry.Items)
        {
            item.ImageUrl = imageUrlService.CreateImageUrl(item.ItemId);
        }

        return new GetPurchaseHistoryDetailsResponse
        {
            SalesEntry = salesEntry
        };
    }
}

public class GetPurchaseHistoryDetailsRequest : IRequest<GetPurchaseHistoryDetailsResponse>
{
    public required int Id { get; set; }
}

public class GetPurchaseHistoryDetailsResponse
{
    public required ResultPurchaseHistory SalesEntry { get; set; }
}

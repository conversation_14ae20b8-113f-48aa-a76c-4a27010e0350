using Bonus.Adapters.EasyShop;
using Bonus.Adapters.EasyShop.Enums;
using Bonus.Adapters.EasyShop.Models;
using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Handlers.Cart;

public class AddCartItemByBarcodeCommand(
    IBonusUser bonusUser,
    IEasyShopAdapter easyShopAdapter,
    IImageUrlService imageUrlService,
    ILogger<AddCartItemByBarcodeCommand> logger
) : IRequestHandler<AddCartItemByBarcodeRequest, AddCartItemByBarcodeResponse>
{
    public async Task<AddCartItemByBarcodeResponse> Handle(AddCartItemByBarcodeRequest request, CancellationToken cancellationToken)
    {
        var easyShopRequest = new EasyShopAddRemoveItemRequest
        {
            Body = new ESAddRemoveItemDto
            {
                Quantity = request.Quantity,
                Barcode = new ESBarcodeDto
                {
                    Data = request.Barcode,
                    Symbology = BarcodeSymbologyType.Unknown
                }
            },
            StoreId = request.StoreId,
            CartId = request.CartId,
            ShopperIdentifier = bonusUser.CardId,
            DeviceId = bonusUser.DeviceId,
            AcceptLanguage = bonusUser.EasyShopCulture,
            CorrelationId = Guid.NewGuid().ToString()
        };

        var result = await easyShopAdapter.AddRemoveItem(easyShopRequest, cancellationToken);

        if (result.Success is false)
        {
            throw new BonusException(nameof(Translations.Cart_AddItemFailed_EasyShop), result.Error?.Message);
        }

        var addedItem = result.Response?.Items
            .Where(x => x.Article.Barcode.Data == request.Barcode)
            .OrderByDescending(x => x.Quantity)
            .FirstOrDefault();

        if (addedItem is null)
        {
            throw new BonusException(nameof(Translations.Cart_AddItemFailed));
        }

        var lsRetailItemId = addedItem.Article.AlternativeIdentities.OrderBy(x => x.Length).FirstOrDefault();

        if (lsRetailItemId.HasValue() is false || lsRetailItemId!.Length > 7)
        {
            logger.LogWarning("Item added by barcode does not have a valid LS Retail Item ID. Barcode: {Barcode}, Description: {Description}, AlternativeIdentities: {AlternativeIdentities}",
                request.Barcode, addedItem.Article.Description, string.Join(",", addedItem.Article.AlternativeIdentities));

            lsRetailItemId = null; // If barcode, don't supply.
        }

        // TODO: Confirm if we can use _1 always.
        var imageUrl = lsRetailItemId is null ? "" : imageUrlService.CreateImageUrl($"{lsRetailItemId}_1");

        // TODO: Other fields?
        return new AddCartItemByBarcodeResponse
        {
            ItemId = addedItem.Id,
            Name = addedItem.Article.Description,
            CatalogueItemId = lsRetailItemId,
            ImageUrl = imageUrl,
            Price = addedItem.Article.Price,
            Measure = addedItem.Article.UnitOfMeasure.ToString(),
            Quantity = addedItem.Quantity,
            DiscountPrice = null,
            DiscountPercentage = null,
            TotalPrice = addedItem.Total,
            CartTotalPrice = result.Response!.Total,
            CartTotalItems = result.Response!.Items.Count
        };
    }
}

public class AddCartItemByBarcodeRequest : IRequest<AddCartItemByBarcodeResponse>
{
    public required string CartId { get; set; }

    public required string StoreId { get; set; }

    public required string Barcode { get; set; }

    public required decimal Quantity { get; set; }
}

public class AddCartItemByBarcodeResponse
{
    public required string ItemId { get; set; }

    public required string Name { get; set; }

    public required string? CatalogueItemId { get; set; }

    public required string ImageUrl { get; set; }

    public required decimal Price { get; set; }

    public required string Measure { get; set; }

    public required decimal Quantity { get; set; }

    public required decimal? DiscountPrice { get; set; }

    public required int? DiscountPercentage { get; set; }

    public required decimal TotalPrice { get; set; }

    public required decimal CartTotalPrice { get; set; }

    public required int CartTotalItems { get; set; }
}

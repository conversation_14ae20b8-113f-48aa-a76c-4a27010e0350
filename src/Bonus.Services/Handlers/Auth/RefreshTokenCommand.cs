using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Models.Requests;
using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Services;
using Bonus.Core.Types.Common.Enums;
using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Auth;

public class RefreshTokenCommand(BonusContext bonusContext, IAuthenticationService authenticationService, ISystemTime systemTime, IBackgroundJobService backgroundJobService, ILSRetailAdapter lsRetailAdapter, IAccountService accountService)
    : IRequestHandler<RefreshTokenRequest, RefreshTokenResponse>
{
    public async Task<RefreshTokenResponse> Handle(RefreshTokenRequest request, CancellationToken cancellationToken)
    {
        if (request.RefreshToken.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.Token_RefreshTokenRequired));
        }

        var refreshTokenData = await bonusContext.RefreshTokens
            .Where(x => x.Token == request.RefreshToken!)
            .Where(x => x.AuthenticationLog.DeviceId == request.DeviceId)
            .Select(x => new
            {
                RefreshToken = x,
                UserId = x.UserId,
                CardId = x.User.CardId,
            })
            .FirstOrDefaultAsync(cancellationToken);

        var refreshToken = refreshTokenData?.RefreshToken;

        if (refreshToken is null)
        {
            await CreateAuthenticationLog("Token not found");
            await bonusContext.SaveChangesAsync();
            throw new BonusException(nameof(Translations.Token_RefreshTokenNotFound));
        }

        if (request.DeviceId.HasValue() is false)
        {
            await CreateAuthenticationLog("Device ID is missing");
            await bonusContext.SaveChangesAsync();
            throw new BonusException(nameof(Translations.Auth_DeviceIdRequired));
        }

        if (refreshToken.ValidUntil < systemTime.UtcNow)
        {
            await CreateAuthenticationLog("Token expired");
            await bonusContext.SaveChangesAsync();
            throw new BonusException(nameof(Translations.Token_RefreshTokenExpired));
        }

        if (refreshToken.UsedOn.HasValue)
        {
            await CreateAuthenticationLog("Token used");
            await bonusContext.SaveChangesAsync();
            throw new BonusException(nameof(Translations.Token_RefreshTokenUsed));
        }

        refreshToken.UsedOn = systemTime.UtcNow;

        var authenticationLog = await CreateAuthenticationLog();
        var loginData = await authenticationService.CreateToken(refreshToken.UserId, refreshTokenData!.CardId, true, authenticationLog);

        await bonusContext.SaveChangesAsync();

        await backgroundJobService.SyncSalesEntries(refreshTokenData.CardId);

        // Check if user has valid email
        var contactRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = refreshTokenData.CardId,
            NumberOfTransactionsReturned = 0,
        };

        var contactResponse = await lsRetailAdapter.GetContactByCardId(contactRequest, cancellationToken);
        var hasValidEmail = contactResponse.Success &&
                           contactResponse.Response?.Contact?.Email is not null &&
                           !accountService.IsTempEmail(contactResponse.Response.Contact.Email);

        return new RefreshTokenResponse()
        {
            Token = loginData.Token,
            RefreshToken = loginData.RefreshToken,
            HasValidEmail = hasValidEmail
        };

        async Task<AuthenticationLog> CreateAuthenticationLog(string? errorMessage = null)
        {
            return await authenticationService.CreateAuthenticationLog(request.RefreshToken!, CredentialType.RefreshToken, refreshTokenData?.UserId, errorMessage, request.IpAddress, request.DeviceManufacturer, request.DeviceModelName, request.DeviceId, request.AppVersion, request.OperatingSystem, request.OperatingSystemVersion);
        }
    }
}

public class RefreshTokenRequest : IRequest<RefreshTokenResponse>
{
    public required string? RefreshToken { get; set; }

    public required string? DeviceId { get; set; }

    public required string? DeviceManufacturer { get; set; }

    public required string? DeviceModelName { get; set; }

    public required string? OperatingSystem { get; set; }

    public required string? OperatingSystemVersion { get; set; }

    public required string? AppVersion { get; set; }

    public required string IpAddress { get; set; }
}

public class RefreshTokenResponse
{
    public required string Token { get; set; }

    public required string RefreshToken { get; set; }

    public required bool HasValidEmail { get; set; }
}

using Bonus.Adapters.HagarId.Models;
using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Enums;
using Bonus.Adapters.LSRetail.Models.Members;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Services;
using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Handlers.Auth;

public class CheckHagarIdAuthCommand(
    IHagarIdAuthenticationService hagarIdAuthenticationService,
    ILSRetailAdapter lsRetailAdapter,
    BonusContext bonusContext,
    IAuthenticationService authenticationService,
    ILogger<CheckHagarIdAuthCommand> logger,
    ISystemTime systemTime,
    IBackgroundJobService backgroundJobService)
    : IRequestHandler<CheckHagarIdAuthRequest, CheckHagarIdAuthResponse>
{
    public const string LsRetailAuthenticator = "HagarAuthenticator";

    private const string TempEmail = "@temp-bonus.is";

    public async Task<CheckHagarIdAuthResponse> Handle(CheckHagarIdAuthRequest request, CancellationToken cancellationToken)
    {
        var sessionData = await GetSessionData(request, cancellationToken);
        var checkAuthResponse = await hagarIdAuthenticationService.CheckAuthenticationStatus(request.SessionId!, cancellationToken);

        if (checkAuthResponse is null)
        {
            logger.LogError("Check auth response is null for session {SessionId}", request.SessionId);

            return new CheckHagarIdAuthResponse
            {
                IsAuthenticated = false,
                Status = "Error"
            };
        }

        if (checkAuthResponse.Status == "Pending")
        {
            logger.LogError("Check auth status is pending for session {SessionId}", request.SessionId);

            return new CheckHagarIdAuthResponse
            {
                IsAuthenticated = false,
                Status = checkAuthResponse.Status
            };
        }

        logger.LogInformation("CheckAuthResponse {HagarId}, {NationalId}, {PhoneNumber}, {Status}", checkAuthResponse.HagarId, checkAuthResponse.NationalId, checkAuthResponse.PhoneNumber, checkAuthResponse.Status);

        if (!checkAuthResponse.NationalId.HasValue())
        {
            await UpdateAuthenticationLog(request.SessionId, null, "HagarId authentication failed - no national ID");

            throw new BonusException(nameof(Translations.Login_HagarIdFailed));
        }

        if (!checkAuthResponse.HagarId.HasValue())
        {
            await UpdateAuthenticationLog(request.SessionId, null, "HagarId authentication failed - no Hagar ID");

            throw new BonusException(nameof(Translations.Login_HagarIdFailed));
        }

        var user = await GetOrCreateUser(request, checkAuthResponse, cancellationToken);

        var contactRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = user.CardId,
            NumberOfTransactionsReturned = 0,
        };

        var contactResponse = await lsRetailAdapter.GetContactByCardId(contactRequest, cancellationToken);

        if (contactResponse.Success is false || contactResponse.Response?.Contact is null)
        {
            throw new BonusException(nameof(Translations.Account_ContactNotFound));
        }

        return await CreateSuccessResponse(sessionData, checkAuthResponse, user);
    }

    private async Task<User> GetOrCreateUser(CheckHagarIdAuthRequest request, HagarIdCheckAuthResponse checkAuthResponse, CancellationToken cancellationToken)
    {
        // Check user by HagarId
        var existingUser = await bonusContext.Users
            .Where(x => x.AuthenticationId == checkAuthResponse.HagarId)
            .Include(x => x.RefreshTokens)
            .FirstOrDefaultAsync(cancellationToken);

        if (existingUser is not null)
        {
            return existingUser;
        }

        var socialLogonRequest = new LSSocialLogonRequest
        {
            Authenticator = LsRetailAuthenticator,
            AuthenticationId = checkAuthResponse.HagarId!,
            DeviceId = request.DeviceId,
            DeviceName = request.DeviceModelName,
            IncludeDetails = true,
        };

        var socialLogonResponse = await lsRetailAdapter.SocialLogon(socialLogonRequest, cancellationToken);

        if (socialLogonResponse.Success)
        {
            var user = await bonusContext.Users
                .Where(x => x.MemberContactId == socialLogonResponse.Response!.Contact!.Id)
                .Where(x => x.AccountId == socialLogonResponse.Response!.Contact!.Account!.Id)
                .Where(x => x.CardId == socialLogonResponse.Response!.Contact!.Cards.First().Id)
                .Include(x => x.RefreshTokens)
                .FirstOrDefaultAsync(cancellationToken);

            if (user is null)
            {
                user = new User
                {
                    MemberContactId = socialLogonResponse.Response!.Contact!.Id,
                    AccountId = socialLogonResponse.Response!.Contact.Account!.Id,
                    CardId = socialLogonResponse.Response!.Contact.Cards.First().Id,
                    AuthenticationId = checkAuthResponse.HagarId,
                    Ssn = checkAuthResponse.NationalId,
                    RefreshTokens = [],
                };

                await bonusContext.Users.AddAsync(user);

                await bonusContext.SaveChangesAsync();
            }

            return user;
        }

        // If social logon fails, create a new user with the HagarId
        var firstName = string.Join(" ", checkAuthResponse.Name!.Split(' ').SkipLast(1));
        var lastName = checkAuthResponse.Name.Split(' ').Last();

        // Check if phone number is associated with HagarId
        if (checkAuthResponse.PhoneNumber.HasValue())
        {
            var phoneSearchRequest = new LSContactSearchRequest
            {
                SearchType = ContactSearchType.PhoneNumber,
                Search = checkAuthResponse.PhoneNumber!,
                MaxNumberOfRowsReturned = 1
            };

            var phoneSearchResponse = await lsRetailAdapter.ContactSearch(phoneSearchRequest, cancellationToken);

            if (phoneSearchResponse.Response?.Contacts.Any() == true)
            {
                logger.LogError("Failed to create contact - user with the same phone number already exists: {PhoneNumber}", checkAuthResponse.PhoneNumber);
                throw new BonusException(nameof(Translations.Login_HagarIdFailed));
            }
        }

        // Sanity check - check if the temp email already exists
        var tempEmail = CreateTempEmail(checkAuthResponse.HagarId!);
        var emailSearchRequest = new LSContactSearchRequest
        {
            SearchType = ContactSearchType.Email,
            Search = tempEmail,
            MaxNumberOfRowsReturned = 1
        };

        var emailSearchResponse = await lsRetailAdapter.ContactSearch(emailSearchRequest, cancellationToken);

        if (emailSearchResponse.Response?.Contacts.Any() == true)
        {
            logger.LogError("Failed to create contact - user with the same temp email already exists: {Email}", tempEmail);
            throw new BonusException(nameof(Translations.Login_HagarIdFailed));
        }

        var createContactRequest = new LSContactCreateRequest
        {
            Contact = new MemberContact()
            {
                UserName = "",
                Password = "",
                Email = tempEmail,
                Authenticator = LsRetailAuthenticator,
                AuthenticationId = checkAuthResponse.HagarId!,
                SendReceiptByEMail = 1,
                Initials = "",
                FirstName = firstName,
                MiddleName = "",
                LastName = lastName,
                Addresses = [
                    new Address
                    {
                        Id = "",
                        Type = 0,
                        Address1 = "",
                        Address2 = "",
                        HouseNo = "",
                        City = "",
                        PostCode = "",
                        StateProvinceRegion = "",
                        Country = "",
                        County = "",
                        CellPhoneNumber = "",
                        PhoneNumber = checkAuthResponse.PhoneNumber
                    }
                ],
                Gender = 0,
                MaritalStatus = 0,
                BirthDay = "/Date(-2208988800000)/",
                LoggedOnToDevice = null,
                Blocked = false,
                BlockedBy = null,
                BlockedReason = null,
                GuestType = null,
                Name = checkAuthResponse.Name,
                Notifications = [],
                PublishedOffers = [],
                Profiles = [
                    new Profile
                    {
                        Description = "Kennitala aðildarfélaga",
                        DataType = 0,
                        DefaultValue = "",
                        Mandatory = true,
                        ContactValue = false,
                        TextValue = checkAuthResponse.NationalId,
                        Id = "KENNITALA"
                    }
                ],
                SalesEntries = [],
                OneLists = [],
                Environment = new Adapters.LSRetail.Models.Members.Environment()
                {
                    Currency = new Currency
                    {
                        Id = "",
                        Prefix = "",
                        Postfix = "",
                        Symbol = "",
                        RoundOffSales = 0.01m,
                        RoundOffAmount = 0.01m,
                        DecimalSeparator = "",
                        ThousandSeparator = "",
                        DecimalPlaces = 2,
                        AmountRoundingMethod = 0,
                        Culture = "",
                        Description = "",
                        SaleRoundingMethod = 0,
                    },
                    PasswordPolicy = "",
                    Version = ""
                },
                AlternateId = null,
                ExternalSystem = null,
                Cards = [],
                Account = null,
                Id = ""
            },
            DoLogin = true
        };

        var createContactResponse = await lsRetailAdapter.CreateContact(createContactRequest, cancellationToken);

        if (createContactResponse.Success is false)
        {
            logger.LogError("Failed to create contact in LSRetail: {ErrorCode} {ErrorMessage}", createContactResponse.Error?.FaultCode, createContactResponse.Error?.FaultMessage);
            throw new BonusException(nameof(Translations.Login_HagarIdFailed));
        }

        var newUser = new User
        {
            MemberContactId = createContactResponse.Response!.Contact!.Id,
            AccountId = createContactResponse.Response!.Contact.Account!.Id,
            CardId = createContactResponse.Response!.Contact.Cards.First().Id,
            AuthenticationId = checkAuthResponse.HagarId,
            Ssn = checkAuthResponse.NationalId,
        };

        await bonusContext.Users.AddAsync(newUser);
        await bonusContext.SaveChangesAsync();

        return newUser;
    }

    private async Task<CheckHagarIdAuthResponse> CreateSuccessResponse(HagarIdSession session, HagarIdCheckAuthResponse checkAuthResponse, User user)
    {
        foreach (var refreshToken in user.RefreshTokens.Where(x => x.UsedOn is null))
        {
            refreshToken.ArchivedTime = systemTime.UtcNow;
        }

        var termsAccepted = !(checkAuthResponse.Terms?.TermsNeedToBeAccepted ?? false);
        var authLog = await UpdateAuthenticationLog(session.SessionId, user);
        var loginData = await authenticationService.CreateToken(user.Id, user.CardId, termsAccepted, user.EmailVerified, authLog);

        session.IsCompleted = true;
        await bonusContext.SaveChangesAsync();

        await backgroundJobService.SyncSalesEntries(user.CardId);

        return new CheckHagarIdAuthResponse
        {
            IsAuthenticated = true,
            Status = checkAuthResponse.Status,
            Token = loginData.Token,
            RefreshToken = loginData.RefreshToken,
            EmailVerified = user.EmailVerified
        };
    }

    private async Task<HagarIdSession> GetSessionData(CheckHagarIdAuthRequest request, CancellationToken cancellationToken)
    {
        if (request.SessionId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.Auth_SessionIdRequired));
        }

        var sessionData = await bonusContext.HagarIdSessions
            .Where(s => s.SessionId == request.SessionId)
            .Where(s => s.ExpiresAt > systemTime.UtcNow)
            .Where(s => s.IsCompleted == false)
            .FirstOrDefaultAsync(cancellationToken);

        if (sessionData is null)
        {
            await bonusContext.SaveChangesAsync();
            throw new BonusException(nameof(Translations.Auth_SessionNotFound));
        }

        return sessionData;
    }

    private async Task<AuthenticationLog> UpdateAuthenticationLog(string sessionId, User? user = null, string? errorMessage = null)
    {
        var log = await bonusContext.AuthenticationLogs
            .Where(s => s.HagarIdSession!.SessionId == sessionId)
            .FirstOrDefaultAsync();

        if (log is null)
        {
            throw new BonusException(nameof(Translations.Auth_SessionNotFound));
        }

        log.User = user;
        log.ErrorMessage = errorMessage;
        log.IsSuccess = errorMessage == null;

        await bonusContext.SaveChangesAsync();

        return log;
    }

    private string CreateTempEmail(string hagarIdentifier)
    {
        return $"{hagarIdentifier}{TempEmail}";
    }

}

public class CheckHagarIdAuthRequest : IRequest<CheckHagarIdAuthResponse>
{
    public required string SessionId { get; set; }

    public required string DeviceId { get; set; }

    public required string DeviceModelName { get; set; }
}

public class CheckHagarIdAuthResponse
{
    public required bool IsAuthenticated { get; set; }

    public required string? Status { get; set; }

    public string? Token { get; set; }

    public string? RefreshToken { get; set; }

    public bool EmailVerified { get; set; }
}

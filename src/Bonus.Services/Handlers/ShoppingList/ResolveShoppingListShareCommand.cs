using Bonus.Adapters.LSRetail.Enums;
using Bonus.Resources;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;

namespace Bonus.Services.Handlers.ShoppingList;

public class ResolveShoppingListShareCommand(IBonusUser bonusUser, IShoppingListService shoppingListService)
    : IRequestHandler<ResolveShoppingListShareRequest, ResolveShoppingListShareResponse>
{
    public async Task<ResolveShoppingListShareResponse> Handle(ResolveShoppingListShareRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        if (request.ShoppingListId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.ShoppingList_IdRequired));
        }

        var list = await shoppingListService.ResolveShoppingListShare(request.ShoppingListId, request.Approve, cancellationToken);

        return new()
        {
            Success = true,
            ShoppingList = list
        };
    }
}

public class ResolveShoppingListShareRequest : IRequest<ResolveShoppingListShareResponse>
{
    public required string ShoppingListId { get; set; }
    public required bool Approve { get; set; }
}

public class ResolveShoppingListShareResponse
{
    public required bool Success { get; set; }
    public required ResultShoppingList? ShoppingList { get; set; }
}

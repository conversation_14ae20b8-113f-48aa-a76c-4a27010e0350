using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Resources;
using Bonus.Services.Handlers.PurchaseHistory;
using Bonus.Services.Services;
using Bonus.Shared.Helpers;
using Bonus.Shared.Types.Common.Exceptions;
using Bonus.Shared.Types.Interfaces;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Home;

public class GetHomeDataQuery(ILSRetailAdapter lsRetailAdapter, IBonusUser bonusUser, BonusContext bonusContext, IImageUrlService imageUrlService)
    : IRequestHandler<GetHomeDataRequest, GetHomeDataResponse>
{
    public async Task<GetHomeDataResponse> Handle(GetHomeDataRequest request, CancellationToken cancellationToken)
    {
        if (bonusUser.CardId.HasValue() is false)
        {
            throw new BonusException(nameof(Translations.UserNotAuthenticated));
        }

        var userName = await GetUserNameAsync(cancellationToken);
        var lastTransaction = await GetLastTransactionAsync(cancellationToken);
        var hasUnreadNotifications = await CheckForUnreadNotifications(cancellationToken);

        return new GetHomeDataResponse
        {
            ProfileName = userName,
            QrCode = bonusUser.CardId ?? "N/A", // TODO: QR code should be generated based on CardId
            LatestPurchase = lastTransaction,
            HasUnreadNotifications = hasUnreadNotifications
        };
    }

    private async Task<string> GetUserNameAsync(CancellationToken cancellationToken)
    {
        var userRequest = new LSGetContactGetByCardIdRequest
        {
            CardId = bonusUser.CardId,
            NumberOfTransactionsReturned = 1
        };

        var userResponse = await lsRetailAdapter.GetContactByCardId(userRequest, cancellationToken);

        if (userResponse.Success is false || userResponse.Response!.Contact is null)
        {
            throw new BonusException(nameof(Translations.Account_ContactNotFound));
        }

        var user = userResponse.Response.Contact;

        return user.Name!;
    }

    private async Task<bool> CheckForUnreadNotifications(CancellationToken cancellationToken)
    {
        return await bonusContext.NotificationRecipients
            .Where(nr => nr.UserId == bonusUser.UserId)
            .Where(nr => nr.ReadTime == null)
            .AnyAsync(cancellationToken);
    }

    private async Task<ResultPurchaseHistory?> GetLastTransactionAsync(CancellationToken cancellationToken)
    {
        var purchaseHistory = await bonusContext.SalesEntries
            .Where(x => x.UserId == bonusUser.UserId)
            .Select(x => new ResultPurchaseHistory
            {
                Id = x.Id,
                PurchaseDate = x.PurchaseDate,
                TotalAmount = x.TotalAmount,
                Title = x.StoreName,
                QrCode = x.ReceiptNumber, // TODO: implement QR code generation,
                Items = x.Items.Select(y => new PurchaseHistoryItemDetail
                {
                    Id = y.Id,
                    ItemId = y.ItemId,
                    Name = y.Name,
                    Quantity = y.Quantity,
                    Price = y.Price,
                }).ToList()
            })
            .OrderByDescending(x => x.Id)
            .FirstOrDefaultAsync(cancellationToken);

        foreach (var item in purchaseHistory?.Items ?? [])
        {
            item.ImageUrl = imageUrlService.CreateImageUrl(item.ItemId);
        }

        return purchaseHistory;
    }
}

public class GetHomeDataRequest : IRequest<GetHomeDataResponse>;

public class GetHomeDataResponse
{
    public required string ProfileName { get; set; }

    public required string QrCode { get; set; }

    public required ResultPurchaseHistory? LatestPurchase { get; set; }

    public required bool HasUnreadNotifications { get; set; }
}

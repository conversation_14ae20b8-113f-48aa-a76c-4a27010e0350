using Bonus.Core.Data;
using Bonus.Core.Services;
using Bonus.Services.Services;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Handlers.Catalogue;

public class SearchCatalogueQuery(IProductSearchService searchService, IImageUrlService imageUrlService, BonusContext dbContext) : IRequestHandler<SearchCatalogueRequest, SearchCatalogueResponse>
{
    public async Task<SearchCatalogueResponse> Handle(SearchCatalogueRequest request, CancellationToken cancellationToken)
    {
        if (request.Limit == null || request.Limit < 1)
        {
            request.Limit = 10;
        }

        var searchResults = await searchService.SearchItemsAsync(request.Query, request.Limit.Value, cancellationToken);
        var searchItemIds = searchResults.Select(i => int.Parse(i.Id)).ToList();

        var products = await dbContext.Products
            .Where(x => searchItemIds.Contains(x.Id))
            .Include(x => x.HierarchyNode)
            .ToListAsync(cancellationToken);

        var results = products
            .Select(x => new SearchResultItem
            {
                Id = x.Id.ToString(),
                Name = x.Description ?? "",
                ImageUrl = imageUrlService.CreateImageUrl(x.ImageId),
                Price = x.UnitPrice,
                Measure = x.SalesUnitOfMeasure ?? "STK",
                CategoryId = x.HierarchyNodeId,
                CategoryName = x.HierarchyNode?.Description,
                Similarity = searchResults.FirstOrDefault(y => y.Id == x.Id.ToString())?.Similarity ?? 0
            })
            .OrderByDescending(x => x.Similarity)
            .ToList();

        return new SearchCatalogueResponse
        {
            Items = results,
            TotalCount = results.Count
        };
    }
}

public class SearchCatalogueRequest : IRequest<SearchCatalogueResponse>
{
    public required string Query { get; set; }

    public int? Limit { get; set; }
}

public class SearchCatalogueResponse
{
    public required List<SearchResultItem> Items { get; set; } = [];

    public required int TotalCount { get; set; }
}

public class SearchResultItem // TODO: This can be either item or category? Or just item?
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string ImageUrl { get; set; }

    public required decimal? Price { get; set; }

    public required string? Measure { get; set; }

    public decimal? DiscountPrice { get; set; }

    public int? DiscountPercentage { get; set; }

    public required string? CategoryId { get; set; }

    public required string? CategoryName { get; set; }

    public float Similarity { get; set; }
}

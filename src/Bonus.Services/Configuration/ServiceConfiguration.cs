using Bonus.Core.Services;
using Bonus.Services.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SmartComponents.LocalEmbeddings;

namespace Bonus.Services.Configuration;

public static class ServiceConfiguration
{
    public static void AddApiServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddMediatR(config =>
        {
            config.RegisterServicesFromAssemblyContaining(typeof(ServiceConfiguration));
        });

        services.AddSingleton<LocalEmbedder>();

        services.AddScoped<IEmailService, EmailService>();
        services.AddScoped<IImageUrlService, ImageUrlService>();
        services.AddScoped<ICatalogueService, CatalogueService>();
        services.AddScoped<IProductSearchService, ProductSearchService>();
        services.AddScoped<IShoppingListService, ShoppingListService>();
        services.AddScoped<IHagarIdAuthenticationService, HagarIdAuthenticationService>();
        services.AddScoped<IProductSyncService, ProductSyncService>();
        services.AddScoped<IHierarchySyncService, HierarchySyncService>();
        services.AddScoped<IProductService, ProductService>();
    }
}

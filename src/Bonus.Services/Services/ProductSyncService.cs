using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Services;
using Bonus.Shared.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Services;

public class ProductSyncService(
    ILSRetailAdapter lsRetailAdapter,
    BonusContext dbContext,
    ISystemTime systemTime,
    ILogger<ProductSyncService> logger) : IProductSyncService
{

    private const int BatchSize = 1000;

    public async Task SyncProductsAsync(CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting full product synchronization");

        var batchResult = await SyncProductsBatchInternalAsync("0", cancellationToken);
        var totalProcessed = batchResult.ProcessedCount;
        var lastKey = batchResult.LastKey;

        logger.LogInformation("Processed batch: {ProcessedCount} items, Total: {TotalProcessed}, LastKey: {LastKey}",
            batchResult.ProcessedCount, totalProcessed, lastKey);

        while (lastKey.HasValue() && batchResult.RecordsRemaining > 0)
        {
            batchResult = await SyncProductsBatchInternalAsync(lastKey, cancellationToken);
            totalProcessed += batchResult.ProcessedCount;
            lastKey = batchResult.LastKey;

            logger.LogInformation("Processed batch: {ProcessedCount} items, Total: {TotalProcessed}, LastKey: {LastKey}",
                batchResult.ProcessedCount, totalProcessed, lastKey);
        }

        logger.LogInformation("Completed full product synchronization. Total processed: {TotalProcessed}", totalProcessed);
    }

    private async Task<BatchSyncResult> SyncProductsBatchInternalAsync(string lastKey, CancellationToken cancellationToken)
    {
        var request = new LSReplEcommItemsRequest
        {
            ReplRequest = new LSReplRequest
            {
                AppId = "BonusApp",
                StoreId = "01",
                BatchSize = BatchSize,
                FullReplication = true,
                LastKey = lastKey,
            }
        };

        var response = await lsRetailAdapter.ReplEcommItems(request, cancellationToken);

        if (response.Response?.Result?.Items == null)
        {
            logger.LogWarning("No items received from LSRetail API");
            return new BatchSyncResult { ProcessedCount = 0, LastKey = "", RecordsRemaining = 0 };
        }

        var items = response.Response.Result.Items;
        var processedCount = 0;
        var syncTime = systemTime.UtcNow;

        var itemIds = items
            .Where(i => int.TryParse(i.Id, out _))
            .Select(i => int.Parse(i.Id))
            .ToList();

        var existingProducts = await dbContext.Products
            .Where(p => itemIds.Contains(p.Id))
            .IgnoreQueryFilters()
            .ToDictionaryAsync(p => p.Id, cancellationToken);

        foreach (var item in items)
        {
            try
            {
                UpsertProduct(item, existingProducts, syncTime);
                processedCount++;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to sync product {ItemId}: {Error}", item.Id, ex.Message);
            }
        }

        await dbContext.SaveChangesAsync(cancellationToken);

        return new BatchSyncResult
        {
            ProcessedCount = processedCount,
            LastKey = response.Response.Result.LastKey,
            RecordsRemaining = response.Response.Result.RecordsRemaining
        };
    }

    private void UpsertProduct(LSReplEcommItem item, Dictionary<int, Product> existingProducts, DateTime syncTime)
    {
        if (!int.TryParse(item.Id, out var itemId))
        {
            logger.LogError("Invalid ItemId format: {ItemId}, skipping product", item.Id);
            return;
        }

        if (existingProducts.TryGetValue(itemId, out var existingProduct))
        {
            UpdateProduct(existingProduct, item, syncTime);
        }
        else
        {
            var newProduct = CreateProduct(item, itemId, syncTime);
            dbContext.Products.Add(newProduct);
        }
    }

    private static void UpdateProduct(Product product, LSReplEcommItem item, DateTime syncTime)
    {
        product.Description = item.Description;
        product.Details = item.Details;
        product.UnitPrice = item.UnitPrice;
        product.SalesUnitOfMeasure = item.SalesUnitOfMeasure;
        product.ItemCategoryCode = item.ItemCategoryCode;

        if(item.IsDeleted)
        {
            product.ArchivedTime ??= syncTime;
        }
        else
        {
            product.ArchivedTime = null;
        }

        product.LastSyncTime = syncTime;
    }

    private static Product CreateProduct(LSReplEcommItem item, int itemId, DateTime syncTime)
    {
        return new Product
        {
            Id = itemId,
            Description = item.Description,
            Details = item.Details,
            UnitPrice = item.UnitPrice,
            SalesUnitOfMeasure = item.SalesUnitOfMeasure,
            ItemCategoryCode = item.ItemCategoryCode,
            ArchivedTime = item.IsDeleted ? syncTime : null,
            LastSyncTime = syncTime,
            CreatedTime = syncTime
        };
    }

    private class BatchSyncResult
    {
        public int ProcessedCount { get; set; }

        public string LastKey { get; set; } = string.Empty;

        public int RecordsRemaining { get; set; }
    }
}

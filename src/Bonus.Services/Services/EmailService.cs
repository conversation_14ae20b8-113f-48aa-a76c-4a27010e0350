using System.Net;
using System.Net.Mail;
using Bonus.Shared.Configuration.Settings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Bonus.Services.Services;

public interface IEmailService
{
    Task SendVerificationEmailAsync(string email, string verificationCode, CancellationToken cancellationToken = default);
}

public class EmailService(IOptionsSnapshot<EmailSettings> emailSettings, ILogger<EmailService> logger) : IEmailService
{
    public async Task SendVerificationEmailAsync(string email, string verificationCode, CancellationToken cancellationToken = default)
    {
        try
        {
            using var client = new SmtpClient(emailSettings.Value.SmtpHost, emailSettings.Value.SmtpPort)
            {
                Credentials = new NetworkCredential(emailSettings.Value.SmtpUsername, emailSettings.Value.SmtpPassword),
                EnableSsl = emailSettings.Value.EnableSsl
            };

            var mailMessage = new MailMessage
            {
                From = new MailAddress(emailSettings.Value.FromEmail, emailSettings.Value.FromName),
                Subject = emailSettings.Value.VerificationSubject,
                Body = string.Format(emailSettings.Value.VerificationTemplate, verificationCode),
                IsBodyHtml = true
            };

            mailMessage.To.Add(email);

            await client.SendMailAsync(mailMessage, cancellationToken);
            
            logger.LogInformation("Verification email sent successfully to {Email}", email);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to send verification email to {Email}", email);
            throw;
        }
    }
}

public class EmailSettings
{
    public required string SmtpHost { get; set; }
    public required int SmtpPort { get; set; }
    public required string SmtpUsername { get; set; }
    public required string SmtpPassword { get; set; }
    public required bool EnableSsl { get; set; }
    public required string FromEmail { get; set; }
    public required string FromName { get; set; }
    public required string VerificationSubject { get; set; }
    public required string VerificationTemplate { get; set; }
}

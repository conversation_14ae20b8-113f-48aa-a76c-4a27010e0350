using Bonus.Shared.Configuration.Settings;
using Bonus.Shared.Helpers;
using Microsoft.Extensions.Options;

namespace Bonus.Services.Services;

public interface IImageUrlService
{
    string CreateImageUrl(string? imageId);
}

public class ImageUrlService(IOptions<EnvironmentSettings> envSettings) : IImageUrlService
{
    public string CreateImageUrl(string? imageId)
    {
        if (imageId.HasValue() is false)
        {
            return string.Empty;
        }

        // If item id is provided, try defaulting to the first image.
        // Since we are only passing stream, we can't determine if the image exists or not.
        if (imageId!.Contains("_") is false)
        {
            imageId += "_1";
        }

        return $"{envSettings.Value.BaseUrl.TrimEnd('/')}/api/images/{imageId}";
    }
}

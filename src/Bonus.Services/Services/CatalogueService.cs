using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Resources;
using Bonus.Shared.Types.Common.Exceptions;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Services.Services;

public interface ICatalogueService
{
    Task<List<CategoryData>> GetCategoriesAsync(CancellationToken cancellationToken = default);
    Task<CategoryData> GetCategoryByIdAsync(string id, CancellationToken cancellationToken = default);
}

public class CatalogueService(BonusContext dbContext, IImageUrlService imageUrlService) : ICatalogueService
{
    public async Task<List<CategoryData>> GetCategoriesAsync(CancellationToken cancellationToken = default)
    {
        var rootNodes = await dbContext.HierarchyNodes
            .Where(x => x.ParentNode == null || x.Indentation == 0)
            .OrderBy(x => x.PresentationOrder)
            .ThenBy(x => x.ChildrenOrder)
            .ToListAsync(cancellationToken);

        var categories = await ParseCategoriesFromDatabase(rootNodes, cancellationToken);

        return categories;
    }

    public async Task<CategoryData> GetCategoryByIdAsync(string id, CancellationToken cancellationToken = default)
    {
        var categoryNode = await dbContext.HierarchyNodes
            .Where(x => x.Id == id)
            .FirstOrDefaultAsync(cancellationToken);

        if (categoryNode is null)
        {
            throw new BonusException(nameof(Translations.Catalogue_CategoryNotFound), id);
        }

        var childNodes = await dbContext.HierarchyNodes
            .Where(x => x.ParentNode == id)
            .OrderBy(x => x.PresentationOrder)
            .ThenBy(x => x.ChildrenOrder)
            .ToListAsync(cancellationToken);

        var childCategories = await ParseCategoriesFromDatabase(childNodes, cancellationToken);

        var category = await CreateCategoryFromNode(categoryNode, cancellationToken);
        category.SubCategories = childCategories;

        if (!category.SubCategories.Any())
        {
            var products = await dbContext.Products
                .Where(x => x.HierarchyNodeId == id)
                .OrderBy(x => x.SortOrder ?? int.MaxValue)
                .ToListAsync(cancellationToken);

            category.Items = products.Select(x => new ItemData
            {
                Id = x.Id.ToString(),
                Name = x.Description ?? "",
                Description = x.Details ?? "",
                ImageUrl = imageUrlService.CreateImageUrl(x.ImageId)
            }).ToList();
        }

        return category;
    }

    private async Task<List<CategoryData>> ParseCategoriesFromDatabase(List<HierarchyNode> nodes, CancellationToken cancellationToken)
    {
        var categories = new List<CategoryData>();

        foreach (var node in nodes)
        {
            var category = await CreateCategoryFromNode(node, cancellationToken);
            categories.Add(category);
        }

        return categories;
    }

    private async Task<CategoryData> CreateCategoryFromNode(HierarchyNode node, CancellationToken cancellationToken)
    {
        var subcategoryCount = await dbContext.HierarchyNodes
            .Where(x => x.ParentNode == node.Id)
            .CountAsync(cancellationToken);

        var itemCount = await dbContext.Products
            .Where(p => p.HierarchyNodeId == node.Id)
            .CountAsync(cancellationToken);

        return new CategoryData
        {
            Id = node.Id,
            Name = node.Description ?? "",
            ImageUrl = imageUrlService.CreateImageUrl(node.ImageId),
            ItemCount = itemCount,
            SubcategoryCount = subcategoryCount
        };
    }
}

public class CategoryData
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string ImageUrl { get; set; }

    public int ItemCount { get; set; }

    public int SubcategoryCount { get; set; }

    public List<CategoryData> SubCategories { get; set; } = [];

    public List<ItemData> Items { get; set; } = [];
}

public class ItemData
{
    public required string Id { get; set; }

    public required string Name { get; set; }

    public required string Description { get; set; }

    public required string ImageUrl { get; set; }
}

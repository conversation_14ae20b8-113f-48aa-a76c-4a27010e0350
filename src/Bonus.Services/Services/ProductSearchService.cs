using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Data.Entities.Search;
using Bonus.Core.Services;
using Bonus.Shared.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SmartComponents.LocalEmbeddings;

namespace Bonus.Services.Services;

public class ProductSearchService(
    BonusContext dbContext,
    LocalEmbedder embedder,
    ILogger<ProductSearchService> logger) : IProductSearchService
{

    private const int BatchSize = 1000;

    public async Task IndexAllItemsAsync(CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting semantic indexing process");


        var totalCount = await dbContext.Products
            .CountAsync(cancellationToken);

        logger.LogInformation("Found {ProductCount} products to index", totalCount);

        var processedCount = 0;
        var processed = 0;

        while (processed < totalCount)
        {
            var products = await dbContext.Products
                .OrderBy(x => x.Id) // Ensure consistent ordering for pagination
                .Skip(processed)
                .Take(BatchSize)
                .ToListAsync(cancellationToken);

            if (!products.Any())
            {
                break;
            }

            await ProcessBatchAsync(products, cancellationToken);

            processedCount += products.Count;
            processed += BatchSize;

            logger.LogInformation("Processed {ProcessedCount}/{TotalCount} products", processedCount, totalCount);
        }

        logger.LogInformation("Completed semantic indexing process. Total processed: {ProcessedCount}", processedCount);
    }

    /// <summary>
    /// Returns closest item ids
    /// </summary>
    /// <param name="query"></param>
    /// <param name="limit"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<List<SearchResultItem>> SearchItemsAsync(string query, int limit = 10, CancellationToken cancellationToken = default)
    {
        var embeddings = await dbContext.ProductSearchEmbeddings
            .Select(x => new { x.ItemId, x.EmbeddingSource, x.Embedding })
            .ToListAsync();

        return LocalEmbedder.FindClosestWithScore(
            embedder.Embed(query),
            embeddings.Select(x => (x.ItemId, new EmbeddingF32(x.Embedding))),
            maxResults: limit)
            .Select(x => new SearchResultItem(x.Item, x.Similarity))
            .ToList();
    }

    private async Task ProcessBatchAsync(List<Product> products, CancellationToken cancellationToken)
    {
        var productIds = products.Select(x => x.Id.ToString()).ToList();

        var existingItems = await dbContext.ProductSearchEmbeddings
            .Where(x => productIds.Contains(x.ItemId))
            .ToDictionaryAsync(x => x.ItemId, cancellationToken);

        foreach (var product in products)
        {
            if (product.Description.HasValue() is false)
            {
                logger.LogWarning("Product {ProductId} has no description, skipping", product.Id);
                continue;
            }

            var embedding = embedder.Embed(product.Description!).Buffer.ToArray();
            var productIdString = product.Id.ToString();

            if (existingItems.TryGetValue(productIdString, out var existingItem))
            {
                existingItem.Embedding = embedding;
                existingItem.EmbeddingSource = product.Description!;
            }
            else
            {
                dbContext.ProductSearchEmbeddings.Add(new ProductSearchEmbedding
                {
                    ItemId = productIdString,
                    EmbeddingSource = product.Description!,
                    Embedding = embedding
                });
            }
        }

        await dbContext.SaveChangesAsync(cancellationToken);
    }

}

using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Services;
using Bonus.Shared.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Services;

public class HierarchySyncService(
    ILSRetailAdapter lsRetailAdapter,
    BonusContext dbContext,
    ISystemTime systemTime,
    ILogger<HierarchySyncService> logger) : IHierarchySyncService
{
    private const int BatchSize = 1000;

    private const string AllProducts = "VÖRUR";

    public async Task SyncNodesAsync(CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting hierarchy nodes synchronization");

        var allLsNodes = await FetchAllNodesFromLSRetailAsync(cancellationToken);
        logger.LogInformation("Fetched {Count} nodes from LSRetail", allLsNodes.Count);

        var existingNodes = await dbContext.HierarchyNodes
            .IgnoreQueryFilters()
            .ToDictionaryAsync(n => n.Id, cancellationToken);

        var syncTime = DateTime.UtcNow;
        var lsNodeIds = allLsNodes.Select(n => n.Id).ToHashSet();

        foreach (var lsNode in allLsNodes)
        {
            if (existingNodes.TryGetValue(lsNode.Id, out var existingNode))
            {
                UpdateNode(existingNode, lsNode, syncTime);
            }
            else
            {
                var newNode = CreateNode(lsNode, syncTime);
                dbContext.HierarchyNodes.Add(newNode);
            }
        }

        foreach (var existingNode in existingNodes.Values)
        {
            if (!lsNodeIds.Contains(existingNode.Id) && existingNode.ArchivedTime == null)
            {
                existingNode.ArchivedTime = syncTime;
                logger.LogInformation("Archiving node {NodeId}", existingNode.Id);
            }
        }

        await dbContext.SaveChangesAsync(cancellationToken);

        logger.LogInformation("Completed hierarchy nodes synchronization. Processed: {Count}", allLsNodes.Count);
    }

    private async Task<List<LSReplEcommHierarchyNode>> FetchAllNodesFromLSRetailAsync(CancellationToken cancellationToken)
    {
        var allNodes = new List<LSReplEcommHierarchyNode>();
        var lastKey = "0";

        while (true)
        {
            var request = new LSReplEcommHierarchyNodeRequest
            {
                ReplRequest = new LSReplRequest
                {
                    AppId = "BonusApp",
                    StoreId = "01",
                    BatchSize = BatchSize,
                    FullReplication = true,
                    LastKey = lastKey,
                }
            };

            var response = await lsRetailAdapter.ReplEcommHierarchyNode(request, cancellationToken);

            if (!response.Success || response.Response?.Result?.Nodes == null)
            {
                logger.LogWarning("No nodes received from LSRetail API. IsSuccess: {Success}", response.Success);
                break;
            }

            allNodes.AddRange(response.Response.Result.Nodes);

            if (response.Response.Result.RecordsRemaining == 0 || !response.Response.Result.LastKey.HasValue())
            {
                return allNodes;
            }

            lastKey = response.Response.Result.LastKey;
        }

        return allNodes;
    }

    public async Task SyncLeafsAsync(CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting hierarchy leafs synchronization");

        var nodeIds = await dbContext.HierarchyNodes
            .Select(x => x.Id)
            .ToHashSetAsync();

        var batchResult = await SyncLeafsBatchInternalAsync("0", nodeIds, cancellationToken);
        var totalProcessed = batchResult.ProcessedCount;
        var lastKey = batchResult.LastKey;

        logger.LogInformation("Processed leafs batch: {ProcessedCount} items, Total: {TotalProcessed}, LastKey: {LastKey}",
            batchResult.ProcessedCount, totalProcessed, lastKey);

        while (lastKey.HasValue() && batchResult.RecordsRemaining > 0)
        {
            batchResult = await SyncLeafsBatchInternalAsync(lastKey, nodeIds, cancellationToken);
            totalProcessed += batchResult.ProcessedCount;
            lastKey = batchResult.LastKey;
        }

        logger.LogInformation("Completed hierarchy leafs synchronization. Total processed: {TotalProcessed}", totalProcessed);
    }



    private async Task<BatchSyncResult> SyncLeafsBatchInternalAsync(string lastKey, HashSet<string> nodeIds, CancellationToken cancellationToken)
    {
        var request = new LSReplEcommHierarchyLeafRequest
        {
            ReplRequest = new LSReplRequest
            {
                AppId = "BonusApp",
                StoreId = "01",
                BatchSize = BatchSize,
                FullReplication = true,
                LastKey = lastKey,
            }
        };

        var response = await lsRetailAdapter.ReplEcommHierarchyLeaf(request, cancellationToken);

        if (!response.Success || response.Response?.Result?.Leafs == null)
        {
            logger.LogWarning("No leafs received from LSRetail API. Success: {Success}", response.Success);
            return new BatchSyncResult { ProcessedCount = 0, LastKey = "", RecordsRemaining = 0 };
        }

        var leafs = response.Response.Result.Leafs;
        var processedCount = 0;
        var syncTime = systemTime.UtcNow;

        var leafIds = leafs.Select(l => l.Id)
            .Where(id => int.TryParse(id, out _)).Select(int.Parse).ToList();

        var products = await dbContext.Products
            .Where(x => leafIds.Contains(x.Id))
            .ToListAsync(cancellationToken);

        var productDict = products.ToDictionary(p => p.Id, p => p);

        foreach (var leaf in leafs)
        {
            try
            {
                if (!int.TryParse(leaf.Id, out var leafIdInt) || !productDict.TryGetValue(leafIdInt, out var product))
                {
                    continue;
                }

                if (!nodeIds.Contains(leaf.NodeId))
                {
                    product.ArchivedTime = syncTime;
                    processedCount++;

                    continue;
                }

                if (product.HierarchyNodeId == null || product.HierarchyNodeId == AllProducts || leaf.NodeId != AllProducts)
                {
                    product.HierarchyNodeId = leaf.NodeId;
                    product.ImageId = leaf.ImageId;
                    product.SortOrder = leaf.SortOrder;
                    product.LastHierarchySyncTime = syncTime;
                }

                processedCount++;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to sync hierarchy leaf {LeafId}: {ErrorMessage}", leaf.Id, ex.Message);
            }
        }

        await dbContext.SaveChangesAsync(cancellationToken);

        return new BatchSyncResult
        {
            ProcessedCount = processedCount,
            LastKey = response.Response.Result.LastKey,
            RecordsRemaining = response.Response.Result.RecordsRemaining
        };
    }

    private static void UpdateNode(HierarchyNode node, LSReplEcommHierarchyNode lsNode, DateTime syncTime)
    {
        node.Description = lsNode.Description;
        node.HierarchyCode = lsNode.HierarchyCode;
        node.ParentNode = lsNode.ParentNode;
        node.ImageId = lsNode.ImageId;
        node.Indentation = lsNode.Indentation;
        node.ChildrenOrder = lsNode.ChildrenOrder;
        node.PresentationOrder = lsNode.PresentationOrder;
        node.LastSyncTime = syncTime;
        node.ArchivedTime = null;
    }

    private static HierarchyNode CreateNode(LSReplEcommHierarchyNode lsNode, DateTime syncTime)
    {
        return new HierarchyNode
        {
            Id = lsNode.Id,
            Description = lsNode.Description,
            HierarchyCode = lsNode.HierarchyCode,
            ParentNode = lsNode.ParentNode,
            ImageId = lsNode.ImageId,
            Indentation = lsNode.Indentation,
            ChildrenOrder = lsNode.ChildrenOrder,
            PresentationOrder = lsNode.PresentationOrder,
            LastSyncTime = syncTime,
        };
    }

    private class BatchSyncResult
    {
        public int ProcessedCount { get; set; }

        public string LastKey { get; set; } = string.Empty;

        public int RecordsRemaining { get; set; }
    }
}

using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Hybrid;

namespace Bonus.Services.Services;

public interface IProductService
{
    Task<List<Product>> GetProductsByIdAsync(List<string> ids, CancellationToken cancellationToken);
}

public class ProductService(BonusContext bonusContext, HybridCache cache) : IProductService
{
    private const string CacheKey = "productService:allProducts";
    private readonly TimeSpan CacheExpiration = TimeSpan.FromMinutes(60);

    public async Task<List<Product>> GetProductsByIdAsync(List<string> ids, CancellationToken cancellationToken)
    {
        var searchableIds = ids
            .Where(x => int.TryParse(x, out _))
            .Select(int.Parse)
            .ToList();

        var allProducts = await GetAllProducts(cancellationToken);

        var products = allProducts
            .Where(x => searchableIds.Contains(x.Id))
            .ToList();

        return products;
    }

    private async Task<List<Product>> GetAllProducts(CancellationToken cancellationToken)
    {
        return await cache.GetOrCreateAsync(
            CacheKey,
            async ct =>
            {
                return await bonusContext.Products
                    .AsNoTracking()
                    .ToListAsync(ct);
            },
            new HybridCacheEntryOptions()
            {
                Expiration = CacheExpiration,
                LocalCacheExpiration = CacheExpiration,
            },
            cancellationToken: cancellationToken);
    }
}

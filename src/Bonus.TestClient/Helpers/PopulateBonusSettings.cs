using Bonus.Core.Settings.Data;
using Bonus.Core.Settings.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Bonus.TestClient.Helpers;

public static class PopulateBonusSettings
{
    public static async Task CreateSettings(IServiceProvider serviceProvider)
    {
        var settingsContext = serviceProvider!.GetRequiredService<BonusSettingsContext>();
        await settingsContext.Database.MigrateAsync();


        var currentSettings = await settingsContext.AppSettings.ToListAsync();

        List<AppSetting> appSettings =
        [
            // DB configuration
            new() { Setting = "ConnectionStrings:BonusContext", Value = "Data Source=.;Initial Catalog=bonus;Integrated Security=SSPI;TrustServerCertificate=True;Connection Timeout=30;MultipleActiveResultSets=False;", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "ConnectionStrings:Hangfire", Value = "Data Source=.;Initial Catalog=bonus;Integrated Security=SSPI;TrustServerCertificate=True;Connection Timeout=30;MultipleActiveResultSets=False;", Version = 1, ValidFrom = DateTime.UtcNow },

            // JWT settings
            new() { Setting = "JwtSettings:Secret", Value = "This should be very secret and populated in a secret way.", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "JwtSettings:Issuer", Value = "https://www.bonus.app", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "JwtSettings:Audience", Value = "https://www.bonus.app", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "JwtSettings:ExpiryMinutes", Value = "30", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "JwtSettings:RefreshTokenExpiryMinutes", Value = "43200", Version = 1, ValidFrom = DateTime.UtcNow },

            // Environment settings
            new() { Setting = "EnvironmentSettings:BaseUrl", Value = "http://localhost:5258", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "EnvironmentSettings:TermsUrl", Value = "https://bonus.is/gripidoggreitt/personuverndarstefna/", Version = 1, ValidFrom = DateTime.UtcNow },

            // Expo settings
            new() { Setting = "ExpoApiSettings:BaseAddress", Value = "https://exp.host", Version = 1, ValidFrom = DateTime.UtcNow },

            // LS Retail settings
            new() { Setting = "BonusApiSettings:LsRetailBaseAddress", Value = "", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "BonusApiSettings:TestCardIdOverride", Value = "", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "BonusApiSettings:StoreNumberAlwaysInStore", Value = "01", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "BonusApiSettings:StoreFakeCoordinateLongitude", Value = "-22.05697", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "BonusApiSettings:StoreFakeCoordinateLatitude", Value = "64.32731", Version = 1, ValidFrom = DateTime.UtcNow },

            // EasyShop/Datema API settings
            new() { Setting = "EasyShopApiSettings:EasyShopBaseAddress", Value = "", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "EasyShopApiSettings:GrantType", Value = "client_credentials", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "EasyShopApiSettings:ClientId", Value = "", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "EasyShopApiSettings:ClientSecret", Value = "", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "EasyShopApiSettings:Scope", Value = "openid", Version = 1, ValidFrom = DateTime.UtcNow },

            // HagarId API settings
            new() { Setting = "HagarIdApiSettings:BaseAddress", Value = "", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "HagarIdApiSettings:StartAuthCode", Value = "", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "HagarIdApiSettings:CheckAuthCode", Value = "", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "HagarIdApiSettings:Company", Value = "", Version = 1, ValidFrom = DateTime.UtcNow },

            // Cloudinary API settings
            new() { Setting = "CloudinaryApiSettings:BaseUrl", Value = "https://api.cloudinary.com", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "CloudinaryApiSettings:CloudName", Value = "", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "CloudinaryApiSettings:ApiKey", Value = "", Version = 1, ValidFrom = DateTime.UtcNow },
            new() { Setting = "CloudinaryApiSettings:ApiSecret", Value = "", Version = 1, ValidFrom = DateTime.UtcNow },
        ];

        foreach (var setting in appSettings)
        {
            if (currentSettings.Any(x => x.Setting == setting.Setting))
            {
                continue;
            }

            Console.WriteLine($"Added \"{setting.Setting}\" to the database.");
            await settingsContext.AppSettings.AddAsync(setting);
        }

        await settingsContext.SaveChangesAsync();
    }
}

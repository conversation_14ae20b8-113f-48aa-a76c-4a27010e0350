using Bonus.Core.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Bonus.TestClient.Helpers;

public class PopulateRecurringJobs
{
    public static async Task CreateEntries(IServiceProvider serviceProvider)
    {
        var ctx = serviceProvider!.GetRequiredService<BonusContext>();
        await ctx.Database.MigrateAsync();

        var currentEntities = await ctx.RecurringJobs.ToListAsync();

        var recurringJobs = new[]
        {
            "sync-sales-entries",
            "sync-catalogue"
        };

        foreach (var job in recurringJobs)
        {
            if (currentEntities.Any(x => x.JobName == job) is false)
            {
                ctx.RecurringJobs.Add(new Core.Data.Entities.RecurringJob
                {
                    JobName = job,
                    IsEnabled = false // TODO: Reminder to turn on jobs manually in db.
                });
            }
        }

        await ctx.SaveChangesAsync();
    }
}
